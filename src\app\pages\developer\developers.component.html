<div class="mb-5 mt-0">
  <app-broker-title></app-broker-title>
</div>

<div class="card mb-5 mb-xl-10 developers-page" [class.rtl-layout]="translationService.getCurrentLanguage() === 'ar'">
  <div class="card-body pt-3 pb-0">
    <div class="d-flex flex-wrap flex-sm-nowrap">
      <div class="flex-grow-1">
        <div class="d-flex justify-content-between align-items-start flex-wrap"
          [class.flex-row-reverse]="translationService.isRTL()">
          <div class="d-flex my-4">
            <h1 class="text-dark-blue fs-2 fw-bolder me-1 mt-3">
              {{ getTranslatedText('DEVELOPERS') }}
            </h1>
          </div>

          <div class="d-flex my-4">
            <form data-kt-search-element="form" class="w-300px position-relative mb-3" autocomplete="off">

              <input type="text" [class]="translationService.isRTL() ?
                  'form-control form-control-flush bg-light border rounded-pill' :
                  'form-control form-control-flush ps-10 bg-light border rounded-pill'"
                [style.padding-right]="translationService.isRTL() ? '40px' : '12px'"
                [style.padding-left]="translationService.isRTL() ? '12px' : '40px'"
                [style.text-align]="translationService.isRTL() ? 'right' : 'left'"
                [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'" name="searchInput"
                [(ngModel)]="searchInput" [placeholder]="getTranslatedText('SEARCH_DEVELOPERS')"
                data-kt-search-element="input" (keyup)="onSearch($event)" />
            </form>
          </div>

          <div class="d-flex h-40px my-4">
            <ul class="nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-5 fw-bolder flex-nowrap"
              [class.flex-row-reverse]="translationService.isRTL()">
              <li class="nav-item">
                <a [class]="translationService.isRTL() ?
                    'nav-link ms-2 pt-0 pb-0 btn' :
                    'nav-link me-2 pt-0 pb-0 btn'" [class.btn-dark-blue]="activeTab === 'all'"
                  [class.text-white]="activeTab === 'all'" [class.btn-light-dark-blue]="activeTab !== 'all'"
                  routerLink="/developer" (click)="setActiveTab('all')">
                  {{ getTranslatedText('ALL_DEVELOPERS') }}
                </a>
              </li>

              <li class="nav-item">
                <a [class]="translationService.isRTL() ?
                    'nav-link ms-2 pt-0 pb-0 btn' :
                    'nav-link me-2 pt-0 pb-0 btn'" [class.btn-active-dark-blue]="activeTab === 'contracted'"
                  [class.btn-light-dark-blue]="activeTab !== 'contracted'" routerLink="/developer"
                  (click)="setActiveTab('contracted')">
                  {{ getTranslatedText('CONTRACTED') }}
                </a>
              </li>

              <li class="nav-item">
                <a [class]="translationService.isRTL() ?
                    'nav-link ms-2 pt-0 pb-0 btn' :
                    'nav-link me-2 pt-0 pb-0 btn'" [class.btn-active-dark-blue]="activeTab === 'not-contracted'"
                  [class.btn-light-dark-blue]="activeTab !== 'not-contracted'" routerLink="/developer"
                  (click)="setActiveTab('not-contracted')">
                  {{ getTranslatedText('NOT_CONTRACTED') }}
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <router-outlet></router-outlet>

    <div class="table-responsive mb-5" [class.rtl-table]="translationService.isRTL()"
      [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
      <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 mt-5"
        [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
        <thead>
          <tr class="fw-bold bg-light-dark-blue text-dark-blue me-1 ms-1">
            <!-- Actions Column - First in RTL, Last in LTR -->
            <th *ngIf="translationService.isRTL()" class="min-w-100px rounded-start"
              [class.text-start]="translationService.isRTL()" [class.ps-4]="translationService.isRTL()"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
              {{ getTranslatedText('ACTIONS') }}
            </th>

            <!-- Status Column -->
            <th class="min-w-100px"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ getTranslatedText('STATUS') }}
            </th>

            <!-- Contract End Date Column -->
            <th class="min-w-150px cursor-pointer" (click)="sortData('contract_end_date')"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ getTranslatedText('CONTRACT_END_DATE') }}
              <span class="ms-1 text-primary fw-bold">{{ getSortArrow('contract_end_date') }}</span>
            </th>

            <!-- Contract Date Column -->
            <th class="min-w-150px cursor-pointer" (click)="sortData('contract_start_date')"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ getTranslatedText('CONTRACT_DATE') }}
              <span class="ms-1 text-primary fw-bold">{{ getSortArrow('contract_start_date') }}</span>
            </th>

            <!-- Contract Duration Column -->
            <th class="min-w-100px cursor-pointer" (click)="sortData('contract_duration')"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ getTranslatedText('CONTRACT_DURATION') }}
              <span class="ms-1 text-primary fw-bold">{{ getSortArrow('contract_duration') }}</span>
            </th>

            <!-- Developer Name Column -->
            <th class="min-w-150px"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ getTranslatedText('DEVELOPER_NAME') }}
            </th>

            <!-- Checkbox Column -->
            <th class="w-25px" [class.ps-4]="!translationService.isRTL()" [class.pe-4]="translationService.isRTL()"
              [class.rounded-start]="!translationService.isRTL()" [class.rounded-end]="translationService.isRTL()">
              <div class="form-check form-check-sm form-check-custom form-check-solid">
                <input class="form-check-input" type="checkbox" value="1" data-kt-check="true"
                  data-kt-check-target=".widget-13-check" />
              </div>
            </th>

            <!-- Actions Column - Last in LTR, First in RTL -->
            <th *ngIf="!translationService.isRTL()" class="min-w-100px rounded-end"
              [class.text-end]="!translationService.isRTL()" [class.pe-4]="!translationService.isRTL()"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
              {{ getTranslatedText('ACTIONS') }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let row of rows">
            <!-- Actions Column - First in RTL, Last in LTR -->
            <td *ngIf="translationService.isRTL()" [class.text-start]="translationService.isRTL()"
              [class.ps-4]="translationService.isRTL()">
              <div class="dropdown d-flex justify-content-start">
                <button type="button" class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary"
                  data-bs-toggle="dropdown" aria-expanded="false">
                  <i class="fa-solid fa-ellipsis-vertical"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-start">
                  <li>
                    <a class="dropdown-item" [routerLink]="['/developer/projects']"
                      [queryParams]="{ developerId: row.developerId }"
                      class="d-flex flex-row-reverse align-items-center">
                      <i class="fa-solid fa-eye ms-2"></i>
                      {{ getTranslatedText('VIEW_DEVELOPER') }}
                    </a>
                  </li>
                </ul>
              </div>
            </td>

            <!-- Status Column -->
            <td [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              <ng-container [ngSwitch]="getDeveloperStatus(row.brokers)">
                <button *ngSwitchCase="getTranslatedText('SEND_CONTRACT_REQUEST')"
                  (click)="openContractModal(contractRequestModal, row)" class="btn btn-sm btn-primary"
                  [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                  {{ getTranslatedText('SEND_CONTRACT_REQUEST') }}
                </button>

                <span *ngSwitchDefault class="fw-bold badge fs-6 fw-semibold fs-4 badge-light-success"
                  [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                  {{ getTranslatedText('CONTRACTED') }}
                </span>
              </ng-container>
            </td>

            <!-- Contract End Date Column -->
            <td [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              <span class="fw-bold badge fs-6 fw-semibold"
                [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                {{ row.contractEndDate }}
              </span>
            </td>

            <!-- Contract Date Column -->
            <td [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              <span class="text-gray-800 fw-semibold d-block mb-1 fs-6"
                [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                {{ row.contractStartDate }}
              </span>
            </td>

            <!-- Contract Duration Column -->
            <td [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              <span class="text-gray-800 fw-semibold d-block mb-1 fs-6"
                [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                {{ row.contractDuration }}
              </span>
            </td>

            <!-- Developer Name Column (Image + Name) -->
            <td [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              <div class="d-flex align-items-center" [class.flex-row-reverse]="translationService.isRTL()"
                [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
                <div class="symbol symbol-45px symbol-circle" [class.me-3]="!translationService.isRTL()"
                  [class.ms-3]="translationService.isRTL()">
                  <img [src]="row.image" alt="img" class="rounded-circle" />
                </div>
                <div class="d-flex flex-column" [class.justify-content-start]="!translationService.isRTL()"
                  [class.justify-content-end]="translationService.isRTL()"
                  [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                  <a [routerLink]="['/developer/projects']" [queryParams]="{ developerId: row.developerId }"
                    class="text-gray-900 fw-bold text-hover-dark-blue fs-6"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                    {{ row.fullName }}
                  </a>
                  <span class="text-muted fs-7"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                    {{ row.numberOfProjects }} {{ getTranslatedText('PROJECTS') }}
                  </span>
                </div>
              </div>
            </td>

            <!-- Checkbox Column -->
            <td [class.ps-4]="!translationService.isRTL()" [class.pe-4]="translationService.isRTL()">
              <div class="form-check form-check-sm form-check-custom form-check-solid">
                <input class="form-check-input widget-13-check" type="checkbox" value="1" />
              </div>
            </td>

            <!-- Actions Column - Last in LTR, First in RTL -->
            <td *ngIf="!translationService.isRTL()" [class.text-end]="!translationService.isRTL()"
              [class.pe-4]="!translationService.isRTL()">
              <div class="dropdown d-flex justify-content-end">
                <button type="button" class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary"
                  data-bs-toggle="dropdown" aria-expanded="false">
                  <i class="fa-solid fa-ellipsis-vertical"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                  <li>
                    <a class="dropdown-item" [routerLink]="['/developer/projects']"
                      [queryParams]="{ developerId: row.developerId }" class="d-flex align-items-center">
                      <i class="fa-solid fa-eye me-2"></i>
                      {{ getTranslatedText('VIEW_DEVELOPER') }}
                    </a>
                  </li>
                </ul>
              </div>
            </td>
          </tr>
        </tbody>
      </table>

      <div class="m-2">
        <app-pagination [totalItems]="page.totalElements" [itemsPerPage]="page.limit" [currentPage]="page.pageNumber"
          (pageChange)="onPageChange($event)">
        </app-pagination>
      </div>
    </div>
  </div>
</div>

<router-outlet></router-outlet>

<!-- Contract Request Modal -->
<ng-template #contractRequestModal let-modal>
  <div class="modal-header" [class.flex-row-reverse]="translationService.isRTL()">
    <h4 class="modal-title" id="modal-basic-title">{{ getTranslatedText('SEND_CONTRACT_REQUEST_MODAL') }}</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="modal.dismiss('closed')"></button>
  </div>
  <div class="modal-body" [class.text-end]="translationService.isRTL()">
    <div class="row">
      <div class="">
        <!-- Document Upload Section -->
        <div class="container p-4 bg-white rounded shadow-sm">
          <!-- Header -->
          <div class="text-center mb-4">
            <h5 class="fw-bold text-primary" style="color: #2e2a7e !important">
              {{ getTranslatedText('UPLOAD_DOCUMENTS') }}
            </h5>
            <p class="text-success mt-2" style="font-size: 12px">
              {{ getTranslatedText('UPLOAD_DOCUMENTS_DESC') }}
            </p>
          </div>

          <!-- Document Upload Cards -->
          <div class="mb-4">
            <!-- Personal Photo -->
            <div class="border border-1 border-primary rounded p-3 mb-3" style="border-style: dashed !important">
              <label for="image" class="d-flex flex-column align-items-center cursor-pointer w-100 m-0">
                <div class="upload-icon mb-2">
                  <i class="fas fa-cloud-upload-alt text-primary" style="font-size: 20px"></i>
                </div>
                <div class="text-center">
                  <p class="mb-1 fw-bold">
                    {{ getTranslatedText('PROFILE_PICTURE') }}

                    <span *ngIf="getFileCount('image') > 0"
                      [class]="translationService.isRTL() ? 'badge bg-success me-2' : 'badge bg-success ms-2'">
                      {{ getFileCount("image") }}
                    </span>
                  </p>

                  <p class="text-muted small mb-0">
                    <span>{{ getTranslatedText('FILE_SIZE') }}</span>
                  </p>
                </div>
                <input type="file" id="image" class="d-none" (change)="onFileChange($event, 'image')"
                  accept="image/*" />
              </label>
            </div>

            <!-- ID Front -->
            <div class="border border-1 border-primary rounded p-3 mb-3" style="border-style: dashed !important">
              <label for="idFront" class="d-flex flex-column align-items-center cursor-pointer w-100 m-0">
                <div class="upload-icon mb-2">
                  <i class="fas fa-cloud-upload-alt text-primary" style="font-size: 20px"></i>
                </div>
                <div class="text-center">
                  <p class="mb-1 fw-bold">
                    {{ getTranslatedText('NATIONAL_ID_FRONT') }}
                    <span *ngIf="getFileCount('idFront') > 0"
                      [class]="translationService.isRTL() ? 'badge bg-success me-2' : 'badge bg-success ms-2'">
                      {{ getFileCount("idFront") }}
                    </span>
                  </p>
                  <p class="text-muted small mb-0">
                    <span>{{ getTranslatedText('FILE_SIZE') }}</span>
                  </p>
                </div>
                <input type="file" id="idFront" class="d-none" (change)="onFileChange($event, 'idFront')"
                  accept="image/*" />
              </label>
            </div>

            <!-- ID Back -->
            <div class="border border-1 border-primary rounded p-3 mb-3" style="border-style: dashed !important">
              <label for="idBack" class="d-flex flex-column align-items-center cursor-pointer w-100 m-0">
                <div class="upload-icon mb-2">
                  <i class="fas fa-cloud-upload-alt text-primary" style="font-size: 20px"></i>
                </div>
                <div class="text-center">
                  <p class="mb-1 fw-bold">
                    {{ getTranslatedText('NATIONAL_ID_BACK') }}
                    <span *ngIf="getFileCount('idBack') > 0"
                      [class]="translationService.isRTL() ? 'badge bg-success me-2' : 'badge bg-success ms-2'">
                      {{ getFileCount("idBack") }}
                    </span>
                  </p>
                  <p class="text-muted small mb-0">
                    <span>{{ getTranslatedText('FILE_SIZE') }}</span>
                  </p>
                </div>
                <input type="file" id="idBack" class="d-none" (change)="onFileChange($event, 'idBack')"
                  accept="image/*" />
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="modal-footer" [class.flex-row-reverse]="translationService.isRTL()">
    <button type="button" class="btn btn-light" (click)="modal.dismiss('Cancel')">
      {{ getTranslatedText('CANCEL') }}
    </button>
    <button type="button" class="btn btn-dark-blue" [disabled]="isSendingRequest || !areAllFilesUploaded()"
      (click)="sendContractRequest()">
      {{ getTranslatedText('SEND_REQUEST') }}
      <span *ngIf="isSendingRequest"
        [class]="translationService.isRTL() ? 'spinner-border spinner-border-sm align-middle me-2' : 'spinner-border spinner-border-sm align-middle ms-2'"></span>
    </button>
  </div>
</ng-template>