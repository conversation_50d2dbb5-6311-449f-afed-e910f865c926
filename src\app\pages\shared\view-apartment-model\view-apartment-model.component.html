<div class="modal fade" id="viewUnitPlanModal" tabindex="-1" aria-labelledby="unitPlanModalLabel" aria-hidden="true"
  [class.rtl-layout]="translationService.getCurrentLanguage() === 'ar'">
  <div class="modal-dialog modal-lg modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="unitPlanModalLabel">{{ getTranslatedText('UNIT_PLAN') }}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"
          [attr.aria-label]="getTranslatedText('CLOSE')"></button>
      </div>
      <div class="modal-body text-center">
        <div *ngIf="selectedUnitPlanImage; else noImage">
          <img [src]="selectedUnitPlanImage" [alt]="getTranslatedText('UNIT_PLAN')" class="img-fluid rounded"
            style="max-height: 500px" />
          <div class="mt-3">
            <p class="text-muted">{{ getTranslatedText('UNIT_PLAN_IMAGE') }}</p>
          </div>
        </div>
        <ng-template #noImage>
          <div class="alert alert-warning">{{ getTranslatedText('NO_IMAGE_AVAILABLE') }}</div>
        </ng-template>
      </div>
    </div>
  </div>
</div>
