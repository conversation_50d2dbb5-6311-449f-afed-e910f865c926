<div class="row mb-5" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
  <div class="col-12">
    <div class="card">
      <div class="card-body">
        <div class="d-flex align-items-center justify-content-between">
          <div class="d-flex align-items-center" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <div class="header-icon" [class.me-4]="!translationService.isRTL()"
              [class.ms-4]="translationService.isRTL()">
              <div class="icon-wrapper bg-light-dark-blue">
                <span class="menu-icon">
                  <img class="mx-auto h-20px w-20px" src="../../../../../../assets/media/broker/subscription2.png"
                    alt="" />
                </span>
              </div>
            </div>
            <div class="header-content" [class.text-end]="translationService.isRTL()"
              [class.text-start]="!translationService.isRTL()">
              <h3 class="header-title mb-1 text-dark-blue"
                [style.font-family]="translationService.isRTL() ? 'Noto Kufi Arabic, sans-serif' : 'inherit'"
                [style.font-size]="translationService.isRTL() ? '1.6rem' : 'inherit'">
                {{ 'SUPER_ADMIN.SETTINGS.PACKAGES.TITLE' | translate }}
              </h3>
              <p class="header-subtitle mb-0"
                [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                [style.font-size]="translationService.isRTL() ? '1rem' : 'inherit'">
                {{ 'SUPER_ADMIN.SETTINGS.PACKAGES.DESCRIPTION' | translate }}
              </p>
            </div>
          </div>

          <button class="btn btn-dark-blue" (click)="createSubscription()"
            [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
            <i class="fas fa-plus" [class.me-1]="!translationService.isRTL()"
              [class.ms-1]="translationService.isRTL()"></i>
            {{ 'SUPER_ADMIN.SETTINGS.PACKAGES.CREATE_NEW' | translate }}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Subscription Cards -->
<div class="row mt-7">
  <div class="col-md-4 mb-2" *ngFor="let subscription of rows">
    <div class="card card-shadow h-100">
      <div class="card-body d-flex flex-center flex-column py-9 px-13">
        <div class="symbol symbol-65px symbol-circle mb-5">
          <img class="mx-auto h-65px" [src]="subscription.image" alt="image" />
        </div>

        <span class="fs-1 text-dark-blue fw-bold mb-2"
          [style.font-family]="translationService.isRTL() ? 'Noto Kufi Arabic, sans-serif' : 'inherit'"
          [style.font-size]="translationService.isRTL() ? '2rem' : 'inherit'">
          {{ subscription.name }}
        </span>
        <span class="fs-3 text-dark-blue fw-bold mb-5"
          [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
          [style.font-size]="translationService.isRTL() ? '1.4rem' : 'inherit'">
          {{ subscription.price }} {{ 'SUPER_ADMIN.SETTINGS.PACKAGES.EGP' | translate }}
          <sub>{{ 'SUPER_ADMIN.SETTINGS.PACKAGES.MONTHLY' | translate }}</sub>
        </span>

        <div class="fw-semibold text-gray-500 mb-6 fs-4 px-13 text-center"
          [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
          [style.font-size]="translationService.isRTL() ? '1rem' : 'inherit'">
          {{ subscription.description }}
        </div>

        <div class="d-flex text-center mb-7" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
          <div class="border border-dashed rounded min-w-50px py-3 px-4 mx-2 mb-3">
            <div class="fs-4 fw-bold text-gray-700"
              [style.font-size]="translationService.isRTL() ? '1.4rem' : 'inherit'">
              {{ subscription.maxSpecializations }}
            </div>
            <div class="fw-semibold text-gray-500"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
              [style.font-size]="translationService.isRTL() ? '0.9rem' : 'inherit'">
              {{ 'SUPER_ADMIN.SETTINGS.PACKAGES.SPECIALIZATIONS' | translate }}
            </div>
          </div>

          <div class="border border-dashed rounded min-w-50px py-3 px-4 mx-2 mb-3">
            <div class="fs-4 fw-bold text-gray-700"
              [style.font-size]="translationService.isRTL() ? '1.4rem' : 'inherit'">
              {{ subscription.maxLocations }}
            </div>
            <div class="fw-semibold text-gray-500"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
              [style.font-size]="translationService.isRTL() ? '0.9rem' : 'inherit'">
              {{ 'SUPER_ADMIN.SETTINGS.PACKAGES.LOCATIONS' | translate }}
            </div>
          </div>

          <div class="border border-dashed rounded min-w-50px py-3 px-4 mx-2 mb-3">
            <div class="fs-4 fw-bold text-gray-700"
              [style.font-size]="translationService.isRTL() ? '1.4rem' : 'inherit'">
              {{ subscription.maxAdvertisements }}
            </div>
            <div class="fw-semibold text-gray-500"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
              [style.font-size]="translationService.isRTL() ? '0.9rem' : 'inherit'">
              {{ 'SUPER_ADMIN.SETTINGS.PACKAGES.ADVERTISEMENTS' | translate }}
            </div>
          </div>
        </div>

        <div class="d-flex text-center mb-7" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
          <div class="border border-dashed rounded min-w-50px py-3 px-4 mx-2 mb-3">
            <div class="fs-4 fw-bold text-gray-700"
              [style.font-size]="translationService.isRTL() ? '1.4rem' : 'inherit'">
              {{ subscription.maxOperations }}
            </div>
            <div class="fw-semibold text-gray-500"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
              [style.font-size]="translationService.isRTL() ? '0.9rem' : 'inherit'">
              {{ 'SUPER_ADMIN.SETTINGS.PACKAGES.OPERATIONS' | translate }}
            </div>
          </div>
        </div>

        <div class="d-flex gap-2 w-100" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
          <button
            class="btn btn-md fw-bolder btn-dark-blue btn-flex flex-fill text-center justify-content-center btn-center"
            (click)="editSubscription(subscription.id)"
            [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
            <i class="fas fa-edit" [class.me-2]="!translationService.isRTL()"
              [class.ms-2]="translationService.isRTL()"></i>
            {{ 'SUPER_ADMIN.SETTINGS.PACKAGES.EDIT' | translate }}
          </button>
          <button class="btn btn-md fw-bolder btn-danger btn-flex btn-center justify-content-center flex-fill"
            (click)="deleteSubscription(subscription.id)"
            [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
            <i class="fas fa-trash" [class.me-2]="!translationService.isRTL()"
              [class.ms-2]="translationService.isRTL()"></i>
            {{ 'SUPER_ADMIN.SETTINGS.PACKAGES.DELETE' | translate }}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>