import { Injectable } from '@angular/core';
import { Validators } from '@angular/forms';
import { BaseConfigService, InputConfig, StepperConfiguration } from '../base-config.service';
import {
  FLOOR_TYPES_OPTIONS,
  UNIT_FACING_TYPES_OPTIONS,
  UNIT_VIEW_TYPES_OPTIONS,
  FINISHING_STATUS_TYPES_OPTIONS,
  DELIVERY_STATUS_TYPES_OPTIONS,
  LEGAL_STATUS_TYPES_OPTIONS,
  OTHER_ACCESSORIES_OPTIONS,
  PAYMENT_METHOD_OPTIONS,
  UNIT_LAYOUT_STATUS_TYPES_OPTIONS,
  BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,
  UNIT_DESIGN_TYPES_OPTIONS,
  FINANCIAL_STATUS_TYPES_OPTIONS,
  FIT_OUT_CONDITION_TYPES_OPTIONS,
  ACTIVITY_TYPES_OPTIONS,
  BUILDING_DEADLINE_TYPES_OPTIONS,
  BUILDING_LICENSE_TYPES_OPTIONS,
  UNIT_DESCRIPTION_TYPES_OPTIONS,
  SELL_FLOOR_TYPES_OPTIONS,
  SELL_OUT_OTHER_ACCESSORIES_OPTIONS,
  BASEMENT_UNIT_LAYOUT_STATUS_TYPES_OPTIONS,
  ROOF_UNIT_LAYOUT_STATUS_TYPES_OPTIONS,
  BUILDING_STATUS_TYPES_OPTIONS,
  SPECIFIC_FINISHING_STATUS_TYPES_OPTIONS,
  SELL_OUT_BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,
  Land_DELIVERY_STATUS_TYPES_OPTIONS,
  Land_OUT_BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,
  SUB_UNIT_OPTIONS,
  SELL_VACATION_UNIT_VIEW_TYPES_OPTIONS,
  FURNISHING_STATUS_OPTIONS,
  SPECIFIC_OTHER_ACCESSORIES_OPTIONS,
  SELL_UNIT_VIEW_TYPES_OPTIONS,
  SELL_ROOF_UNIT_LAYOUT_STATUS_TYPES_OPTIONS,
  SELL_UNIT_DESIGN_TYPES_OPTIONS,
} from '../../stepper-modal.constants';

@Injectable({
  providedIn: 'root'
})
export class SellOutsideCompoundConfigService extends BaseConfigService {

  // ============================================================================
  // SELL CONFIGURATIONS (Property owner looking to sell)
  // ============================================================================

  /**
   * Create sell-specific location inputs for sell outside compound scenarios
   */
  private createSellOutsideCompoundLocationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 2,
        name: 'cityId',
        type: 'select',
        label: 'City',
        options: [],
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'areaId',
        type: 'select',
        label: 'Area',
        options: [],
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'subAreaId',
        type: 'select',
        label: 'Sub Area',
        options: [],
        validators: [],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'detailedAddress',
        type: 'text',
        label: 'Detailed Address',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'addressLink',
        type: 'url',
        label: 'Address Link',
        validators: [Validators.pattern(/^https?:\/\/.+/)],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create sell-specific location inputs for mall-based units (administrative_units, medical_clinics)
   */
  private createSellOutsideCompoundMallLocationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 2,
        name: 'cityId',
        type: 'select',
        label: 'City',
        options: [],
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'areaId',
        type: 'select',
        label: 'Area',
        options: [],
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'subAreaId',
        type: 'select',
        label: 'Sub Area',
        options: [],
        validators: [],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'mallName',
        type: 'text',
        label: 'Mall Name',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'detailedAddress',
        type: 'text',
        label: 'Detailed Address',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'addressLink',
        type: 'url',
        label: 'Address Link',
        validators: [Validators.pattern(/^https?:\/\/.+/)],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create sell outside compound unit information inputs for apartments
   */
  private createSellOutsideCompoundApartmentsUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: SELL_FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floorNumber',
        type: 'number',
        label: 'Floor Number',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitFacing',
        type: 'select',
        label: 'Unit Facing',
        options: UNIT_FACING_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: SELL_UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryDate',
        type: 'date',
        label: 'Delivery Date',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: SELL_OUT_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create sell outside compound unit information inputs for duplexes
   */
  private createSellOutsideCompoundDuplexesUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: SELL_FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floorNumber',
        type: 'number',
        label: 'Floor Number',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'gardenArea',
        type: 'number',
        label: 'Garden Area (m²)',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitFacing',
        type: 'select',
        label: 'Unit Facing',
        options: UNIT_FACING_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: SELL_UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryDate',
        type: 'date',
        label: 'Delivery Date',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'legalStatus',
        type: 'select',
        label: 'Legal Status',
        options: LEGAL_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: SELL_OUT_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create sell outside compound unit information inputs for studios
   */
  private createSellOutsideCompoundStudiosUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: SELL_FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floorNumber',
        type: 'number',
        label: 'Floor Number',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitFacing',
        type: 'select',
        label: 'Unit Facing',
        options: UNIT_FACING_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: SELL_UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryDate',
        type: 'date',
        label: 'Delivery Date',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: SELL_OUT_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create sell outside compound unit information inputs for penthouses
   */
  private createSellOutsideCompoundPenthousesUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'terraceArea',
        type: 'number',
        label: 'Terrace Area (m²)',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitFacing',
        type: 'select',
        label: 'Unit Facing',
        options: UNIT_FACING_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: SELL_UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryDate',
        type: 'date',
        label: 'Delivery Date',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'legalStatus',
        type: 'select',
        label: 'Legal Status',
        options: LEGAL_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: SELL_OUT_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create sell outside compound unit information inputs for basements
   */
  private createSellOutsideCompoundBasementsUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitLayoutStatus',
        type: 'select',
        label: 'Unit Layout Status',
        options: BASEMENT_UNIT_LAYOUT_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryDate',
        type: 'date',
        label: 'Delivery Date',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'legalStatus',
        type: 'select',
        label: 'Legal Status',
        options: LEGAL_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: SELL_OUT_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create sell outside compound unit information inputs for roofs
   */
  private createSellOutsideCompoundRoofsUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitLayoutStatus',
        type: 'select',
        label: 'Unit Layout Status',
        options: SELL_ROOF_UNIT_LAYOUT_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingLayoutStatus',
        type: 'select',
        label: 'Building Layout Status',
        options: BASEMENT_UNIT_LAYOUT_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryDate',
        type: 'date',
        label: 'Delivery Date',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'legalStatus',
        type: 'select',
        label: 'Legal Status',
        options: LEGAL_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: SELL_OUT_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create sell outside compound unit information inputs for standalone villas
   */
  private createSellOutsideCompoundStandaloneVillasUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'numberOfFloors',
        type: 'number',
        label: 'Number of Floors',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
       {
        step: 3,
        name: 'buildingArea',
        type: 'number',
        label: 'Building Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'groundArea',
        type: 'number',
        label: 'Ground Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitDesign',
        type: 'select',
        label: 'Unit Design',
        options: SELL_UNIT_DESIGN_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingStatus',
        type: 'select',
        label: 'Building Status',
        options: BUILDING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: SELL_UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: SELL_OUT_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create sell outside compound unit information inputs for administrative units
   */
  private createSellOutsideCompoundAdministrativeUnitsUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: SELL_FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floorNumber',
        type: 'number',
        label: 'Floor Number',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: SELL_UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryDate',
        type: 'date',
        label: 'Delivery Date',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: SELL_OUT_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create sell outside compound unit information inputs for medical clinics
   */
  private createSellOutsideCompoundMedicalClinicsUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: SELL_FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floorNumber',
        type: 'number',
        label: 'Floor Number',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: SELL_UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryDate',
        type: 'date',
        label: 'Delivery Date',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: SELL_OUT_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create sell outside compound unit information inputs for pharmacies
   */
  private createSellOutsideCompoundPharmaciesUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: SELL_FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floorNumber',
        type: 'number',
        label: 'Floor Number',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: SELL_UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: SPECIFIC_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryDate',
        type: 'date',
        label: 'Delivery Date',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'fitOutCondition',
        type: 'select',
        label: 'Fit Out Condition',
        options: FIT_OUT_CONDITION_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: SELL_OUT_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create sell outside compound unit information inputs for commercial stores
   */
  private createSellOutsideCompoundCommercialStoresUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: SELL_FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floorNumber',
        type: 'number',
        label: 'Floor Number',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: SELL_UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: SPECIFIC_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryDate',
        type: 'date',
        label: 'Delivery Date',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'shopActivity',
        type: 'text',
        label: 'Activity',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: SELL_OUT_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create sell outside compound unit information inputs for factory lands
   */
  private createSellOutsideCompoundFactoryLandsUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building / Land Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'numberOfFloors',
        type: 'number',
        label: 'Number of Floors',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'groundArea',
        type: 'number',
        label: 'Ground Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingArea',
        type: 'number',
        label: 'Building Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingLayoutStatus',
        type: 'select',
        label: 'Building Layout Status',
        options: SELL_OUT_BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'shopActivity',
        type: 'text',
        label: 'Activity',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'fitOutCondition',
        type: 'select',
        label: 'Fit Out Condition',
        options: FIT_OUT_CONDITION_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create sell outside compound unit information inputs for warehouses
   */
  private createSellOutsideCompoundWarehousesUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building / Land Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'numberOfFloors',
        type: 'number',
        label: 'Number of Floors',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'groundArea',
        type: 'number',
        label: 'Ground Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingArea',
        type: 'number',
        label: 'Building Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingLayoutStatus',
        type: 'select',
        label: 'Building Layout Status',
        options: SELL_OUT_BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'shopActivity',
        type: 'text',
        label: 'Activity',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'fitOutCondition',
        type: 'select',
        label: 'Fit Out Condition',
        options: FIT_OUT_CONDITION_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create sell outside compound unit information inputs for commercial administrative buildings
   */
  private createSellOutsideCompoundCommercialAdministrativeBuildingsUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'numberOfFloors',
        type: 'number',
        label: 'Number of Floors',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'groundArea',
        type: 'number',
        label: 'Ground Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingArea',
        type: 'number',
        label: 'Building Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingLayoutStatus',
        type: 'select',
        label: 'Building Layout Status',
        options: SELL_OUT_BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingLicense',
        type: 'select',
        label: 'Building License',
        options: BUILDING_LICENSE_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingDeadline',
        type: 'select',
        label: 'Building Deadline',
        options: BUILDING_DEADLINE_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'activity',
        type: 'select',
        label: 'Activity',
        options: ACTIVITY_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: SELL_UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create sell outside compound unit information inputs for residential buildings
   */
  private createSellOutsideCompoundResidentialBuildingsUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Land Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'groundArea',
        type: 'number',
        label: 'Ground Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingArea',
        type: 'number',
        label: 'Building Area (m²)',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingLayoutStatus',
        type: 'select',
        label: 'Building Layout Status',
        options: Land_OUT_BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingLicense',
        type: 'select',
        label: 'Building License',
        options: BUILDING_LICENSE_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingDeadline',
        type: 'select',
        label: 'Building Deadline',
        options: BUILDING_DEADLINE_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Land View',
        options: SELL_UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryDate',
        type: 'date',
        label: 'Delivery Date',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitDescription',
        type: 'select',
        label: 'Land Description',
        options: UNIT_DESCRIPTION_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: Land_DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  private createSellOutsideCompoundVacationVillasInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'villageName',
        type: 'text',
        label: 'Village Name',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'subUnitType',
        type: 'select',
        label: 'SubUnit Type',
        options: SUB_UNIT_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'numberOfFloors',
        type: 'number',
        label: 'Number of Floors',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'View',
        options: SELL_VACATION_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Furnishing Status',
        options: FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  private createSellOutsideCompoundChaletsInformationInputs(stepperModal: any): InputConfig[] {
   return [
      {
        step: 3,
        name: 'villageName',
        type: 'text',
        label: 'Village Name',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'subUnitType',
        type: 'select',
        label: 'SubUnit Type',
        options: SUB_UNIT_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floorNumber',
        type: 'number',
        label: 'Floor Number',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'View',
        options: SELL_VACATION_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitFacing',
        type: 'select',
        label: 'Unit Facing',
        options: UNIT_FACING_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Furnishing Status',
        options: FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: SPECIFIC_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create media inputs for sell outside compound
   */
  private createSellOutsideCompoundMediaInputs(): InputConfig[] {
    return [
      {
        step: 4,
        name: 'mainImage',
        type: 'file',
        label: 'Main Image',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 4,
        name: 'unitInMasterPlanImage',
        type: 'file',
        label: 'Unit in Master Plan Image',
        validators: [],
        visibility: () => true,
      },
      {
        step: 4,
        name: 'galleryImages',
        type: 'file',
        label: 'Gallery Images',
        validators: [],
        visibility: () => true,
      },
      {
        step: 4,
        name: 'video',
        type: 'file',
        label: 'Video',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create sell outside compound financial inputs
   */
  private createSellOutsideCompoundFinancialInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 5,
        name: 'paymentMethod',
        type: 'select',
        label: 'Payment Method',
        options: PAYMENT_METHOD_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 5,
        name: 'unitPriceSuggestions',
        type: 'checkbox',
        label: 'Unit Price Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 5,
        name: 'unitPrice',
        type: 'number',
        label: 'Unit Price',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create sell outside compound financial inputs with financial status (for mall units)
   */
  private createSellOutsideCompoundMallFinancialInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 5,
        name: 'paymentMethod',
        type: 'select',
        label: 'Payment Method',
        options: PAYMENT_METHOD_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 5,
        name: 'unitPriceSuggestions',
        type: 'checkbox',
        label: 'Unit Price Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 5,
        name: 'unitPrice',
        type: 'number',
        label: 'Unit Price',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 5,
        name: 'financialStatus',
        type: 'select',
        label: 'Financial Status',
        options: FINANCIAL_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create sell outside compound financial inputs with financial status (for industrial properties)
   */
  private createSellOutsideCompoundIndustrialFinancialInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 5,
        name: 'paymentMethod',
        type: 'select',
        label: 'Payment Method',
        options: PAYMENT_METHOD_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 5,
        name: 'unitPriceSuggestions',
        type: 'checkbox',
        label: 'Unit Price Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 5,
        name: 'unitPrice',
        type: 'number',
        label: 'Unit Price',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 5,
        name: 'financialStatus',
        type: 'select',
        label: 'Financial Status',
        options: FINANCIAL_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
    ];
  }

  /**
   * Configuration for sell outside compound apartments
   */
  private createSellOutsideCompoundApartmentsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createSellOutsideCompoundLocationInputs(stepperModal),
      ...this.createSellOutsideCompoundApartmentsUnitInformationInputs(stepperModal),
      ...this.createSellOutsideCompoundMediaInputs(),
      ...this.createSellOutsideCompoundFinancialInputs(stepperModal),
    ];

    return config;
  }

  /**
   * Configuration for sell outside compound duplexes
   */
  private createSellOutsideCompoundDuplexesConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createSellOutsideCompoundLocationInputs(stepperModal),
      ...this.createSellOutsideCompoundDuplexesUnitInformationInputs(stepperModal),
      ...this.createSellOutsideCompoundMediaInputs(),
      ...this.createSellOutsideCompoundFinancialInputs(stepperModal),
    ];

    return config;
  }

  /**
   * Configuration for sell outside compound studios
   */
  private createSellOutsideCompoundStudiosConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createSellOutsideCompoundLocationInputs(stepperModal),
      ...this.createSellOutsideCompoundStudiosUnitInformationInputs(stepperModal),
      ...this.createSellOutsideCompoundMediaInputs(),
      ...this.createSellOutsideCompoundFinancialInputs(stepperModal),
    ];

    return config;
  }

  /**
   * Configuration for sell outside compound penthouses
   */
  private createSellOutsideCompoundPenthousesConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createSellOutsideCompoundLocationInputs(stepperModal),
      ...this.createSellOutsideCompoundPenthousesUnitInformationInputs(stepperModal),
      ...this.createSellOutsideCompoundMediaInputs(),
      ...this.createSellOutsideCompoundFinancialInputs(stepperModal),
    ];

    return config;
  }

  /**
   * Configuration for sell outside compound basements
   */
  private createSellOutsideCompoundBasementsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createSellOutsideCompoundLocationInputs(stepperModal),
      ...this.createSellOutsideCompoundBasementsUnitInformationInputs(stepperModal),
      ...this.createSellOutsideCompoundMediaInputs(),
      ...this.createSellOutsideCompoundFinancialInputs(stepperModal),
    ];

    return config;
  }

  /**
   * Configuration for sell outside compound roofs
   */
  private createSellOutsideCompoundRoofsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createSellOutsideCompoundLocationInputs(stepperModal),
      ...this.createSellOutsideCompoundRoofsUnitInformationInputs(stepperModal),
      ...this.createSellOutsideCompoundMediaInputs(),
      ...this.createSellOutsideCompoundFinancialInputs(stepperModal),
    ];

    return config;
  }

  /**
   * Configuration for sell outside compound standalone villas
   */
  private createSellOutsideCompoundStandaloneVillasConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createSellOutsideCompoundLocationInputs(stepperModal),
      ...this.createSellOutsideCompoundStandaloneVillasUnitInformationInputs(stepperModal),
      ...this.createSellOutsideCompoundMediaInputs(),
      ...this.createSellOutsideCompoundFinancialInputs(stepperModal),
    ];

    return config;
  }

  /**
   * Configuration for sell outside compound administrative units
   */
  private createSellOutsideCompoundAdministrativeUnitsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createSellOutsideCompoundMallLocationInputs(stepperModal),
      ...this.createSellOutsideCompoundAdministrativeUnitsUnitInformationInputs(stepperModal),
      ...this.createSellOutsideCompoundMediaInputs(),
      ...this.createSellOutsideCompoundMallFinancialInputs(stepperModal),
    ];

    return config;
  }

  /**
   * Configuration for sell outside compound medical clinics
   */
  private createSellOutsideCompoundMedicalClinicsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createSellOutsideCompoundMallLocationInputs(stepperModal),
      ...this.createSellOutsideCompoundMedicalClinicsUnitInformationInputs(stepperModal),
      ...this.createSellOutsideCompoundMediaInputs(),
      ...this.createSellOutsideCompoundMallFinancialInputs(stepperModal),
    ];

    return config;
  }

  /**
   * Configuration for sell outside compound pharmacies
   */
  private createSellOutsideCompoundPharmaciesConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createSellOutsideCompoundMallLocationInputs(stepperModal),
      ...this.createSellOutsideCompoundPharmaciesUnitInformationInputs(stepperModal),
      ...this.createSellOutsideCompoundMediaInputs(),
      ...this.createSellOutsideCompoundMallFinancialInputs(stepperModal),
    ];

    return config;
  }

  /**
   * Configuration for sell outside compound commercial stores
   */
  private createSellOutsideCompoundCommercialStoresConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createSellOutsideCompoundMallLocationInputs(stepperModal),
      ...this.createSellOutsideCompoundCommercialStoresUnitInformationInputs(stepperModal),
      ...this.createSellOutsideCompoundMediaInputs(),
      ...this.createSellOutsideCompoundMallFinancialInputs(stepperModal),
    ];

    return config;
  }

  /**
   * Configuration for sell outside compound factory lands
   */
  private createSellOutsideCompoundFactoryLandsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createSellOutsideCompoundLocationInputs(stepperModal),
      ...this.createSellOutsideCompoundFactoryLandsUnitInformationInputs(stepperModal),
      ...this.createSellOutsideCompoundMediaInputs(),
      ...this.createSellOutsideCompoundIndustrialFinancialInputs(stepperModal),
    ];

    return config;
  }

  /**
   * Configuration for sell outside compound warehouses
   */
  private createSellOutsideCompoundWarehousesConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createSellOutsideCompoundLocationInputs(stepperModal),
      ...this.createSellOutsideCompoundWarehousesUnitInformationInputs(stepperModal),
      ...this.createSellOutsideCompoundMediaInputs(),
      ...this.createSellOutsideCompoundIndustrialFinancialInputs(stepperModal),
    ];

    return config;
  }

  /**
   * Configuration for sell outside compound commercial administrative buildings
   */
  private createSellOutsideCompoundCommercialAdministrativeBuildingsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createSellOutsideCompoundLocationInputs(stepperModal),
      ...this.createSellOutsideCompoundCommercialAdministrativeBuildingsUnitInformationInputs(stepperModal),
      ...this.createSellOutsideCompoundMediaInputs(),
      ...this.createSellOutsideCompoundIndustrialFinancialInputs(stepperModal),
    ];

    return config;
  }

  /**
   * Configuration for sell outside compound residential buildings
   */
  private createSellOutsideCompoundResidentialBuildingsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createSellOutsideCompoundLocationInputs(stepperModal),
      ...this.createSellOutsideCompoundResidentialBuildingsUnitInformationInputs(stepperModal),
      ...this.createSellOutsideCompoundMediaInputs(),
      ...this.createSellOutsideCompoundIndustrialFinancialInputs(stepperModal),
    ];

    return config;
  }

  private createSellOutsideCompoundVacationVillasConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createSellOutsideCompoundLocationInputs(stepperModal),
      ...this.createSellOutsideCompoundVacationVillasInformationInputs(stepperModal),
      ...this.createSellOutsideCompoundMediaInputs(),
      ...this.createSellOutsideCompoundIndustrialFinancialInputs(stepperModal),
    ];

    return config;
  }

  private createSellOutsideCompoundChaletsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createSellOutsideCompoundLocationInputs(stepperModal),
      ...this.createSellOutsideCompoundChaletsInformationInputs(stepperModal),
      ...this.createSellOutsideCompoundMediaInputs(),
      ...this.createSellOutsideCompoundIndustrialFinancialInputs(stepperModal),
    ];

    return config;
  }

  // ============================================================================
  // PUBLIC API
  // ============================================================================

  /**
   * Get input configurations for sell outside compound cases
   */
  getInputConfigs(stepperModal: any): StepperConfiguration[] {
    return [
      // SELL OUTSIDE COMPOUND CONFIGURATIONS
      {
        key: 'purchase_sell_outside_compound_sell_apartments',
        value: this.createSellOutsideCompoundApartmentsConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_sell_duplexes',
        value: this.createSellOutsideCompoundDuplexesConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_sell_studios',
        value: this.createSellOutsideCompoundStudiosConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_sell_penthouses',
        value: this.createSellOutsideCompoundPenthousesConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_sell_basements',
        value: this.createSellOutsideCompoundBasementsConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_sell_roofs',
        value: this.createSellOutsideCompoundRoofsConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_sell_standalone_villas',
        value: this.createSellOutsideCompoundStandaloneVillasConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_sell_residential_buildings',
        value: this.createSellOutsideCompoundStandaloneVillasConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_sell_administrative_units',
        value: this.createSellOutsideCompoundAdministrativeUnitsConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_sell_medical_clinics',
        value: this.createSellOutsideCompoundMedicalClinicsConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_sell_pharmacies',
        value: this.createSellOutsideCompoundPharmaciesConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_sell_commercial_stores',
        value: this.createSellOutsideCompoundCommercialStoresConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_sell_factory_lands',
        value: this.createSellOutsideCompoundFactoryLandsConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_sell_warehouses',
        value: this.createSellOutsideCompoundWarehousesConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_sell_commercial_administrative_buildings',
        value: this.createSellOutsideCompoundCommercialAdministrativeBuildingsConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_sell_residential_lands',
        value: this.createSellOutsideCompoundResidentialBuildingsConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_sell_vacation_villa',
        value: this.createSellOutsideCompoundVacationVillasConfig(stepperModal),
      },
      {
        key: 'purchase_sell_outside_compound_sell_chalets',
        value: this.createSellOutsideCompoundChaletsConfig(stepperModal),
      },
    ];
  }

  /**
   * Get all available sell outside compound configuration keys
   */
  getSellOutsideCompoundConfigurationKeys(): string[] {
    return [
      'purchase_sell_outside_compound_sell_apartments',
      'purchase_sell_outside_compound_sell_duplexes',
      'purchase_sell_outside_compound_sell_studios',
      'purchase_sell_outside_compound_sell_penthouses',
      'purchase_sell_outside_compound_sell_basements',
      'purchase_sell_outside_compound_sell_roofs',
      'purchase_sell_outside_compound_sell_standalone_villas',
      'purchase_sell_outside_compound_sell_administrative_units',
      'purchase_sell_outside_compound_sell_medical_clinics',
      'purchase_sell_outside_compound_sell_pharmacies',
      'purchase_sell_outside_compound_sell_commercial_stores',
      'purchase_sell_outside_compound_sell_factory_lands',
      'purchase_sell_outside_compound_sell_warehouses',
      'purchase_sell_outside_compound_sell_vacation_villa',
      'purchase_sell_outside_compound_sell_chalets',
    ];
  }

  /**
   * Check if a key is sell outside compound configuration
   */
  isSellOutsideCompoundConfiguration(key: string): boolean {
    return key.includes('purchase_sell_outside_compound_sell_');
  }
}
