import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { SharedModule } from 'src/app/_metronic/shared/shared.module';
import { NgApexchartsModule } from 'ng-apexcharts';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import {
  BaseChartDirective,
  provideCharts,
  withDefaultRegisterables,
} from 'ng2-charts';

import { SuperAdminDashboardComponent } from './super-admin-dashboard.component';
import { AnalysisCardComponent } from './components/analysis-card/analysis-card.component';
import { ProjectPieChartComponent } from './components/project-pie-chart/project-pie-chart.component';
import { ContractRequestsChartComponent } from './components/contract-requests-chart/contract-requests-chart.component';

// Dashboard Components
import { GeneralDashboardComponent } from './components/general-dashboard/general-dashboard.component';
import { DevelopersDashboardComponent } from './components/developers-dashboard/developers-dashboard.component';
import { BrokersDashboardComponent } from './components/brokers-dashboard/brokers-dashboard.component';
import { ClientsDashboardComponent } from './components/clients-dashboard/clients-dashboard.component';
import { ArabicNumbersPipe } from '../../../shared/pipes/arabic-numbers.pipe';

@NgModule({
  declarations: [
    SuperAdminDashboardComponent,
    AnalysisCardComponent,
    ProjectPieChartComponent,
    ContractRequestsChartComponent,
    // Dashboard Components
    GeneralDashboardComponent,
    DevelopersDashboardComponent,
    BrokersDashboardComponent,
    ClientsDashboardComponent
  ],
  imports: [
    CommonModule,
    RouterModule,
    SharedModule,
    NgApexchartsModule,
    FormsModule,
    TranslateModule,
    BaseChartDirective,
    ArabicNumbersPipe
  ],
  providers: [provideCharts(withDefaultRegisterables())],
  exports: [
    SuperAdminDashboardComponent,
    AnalysisCardComponent,
    ProjectPieChartComponent,
    ContractRequestsChartComponent,
    // Dashboard Components
    GeneralDashboardComponent,
    DevelopersDashboardComponent,
    BrokersDashboardComponent,
    ClientsDashboardComponent
  ]
})
export class SuperAdminDashboardModule { }
