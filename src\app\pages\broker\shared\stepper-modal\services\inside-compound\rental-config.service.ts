import { Injectable } from '@angular/core';
import { Validators } from '@angular/forms';
import { BaseConfigService, InputConfig, StepperConfiguration } from '../base-config.service';
import {
  UNIT_VIEW_TYPES_OPTIONS,
  FINISHING_STATUS_TYPES_OPTIONS,
  OTHER_ACCESSORIES_OPTIONS,
  FURNISHING_STATUS_OPTIONS,
  RENT_RECURRENCE_OPTIONS,
  REQUIRED_INSURANCE_TYPES_OPTIONS,
  OTHER_EXPENSES_OPTIONS,
  FLOOR_TYPES_OPTIONS,
  FIT_OUT_CONDITION_TYPES_OPTIONS,
  ACTIVITY_TYPES_OPTIONS,
  SUB_UNIT_OPTIONS,
  SELL_FLOOR_TYPES_OPTIONS,
  SELL_VACATION_UNIT_VIEW_TYPES_OPTIONS,
  UNIT_FACING_TYPES_OPTIONS,
  SPECIFIC_OTHER_ACCESSORIES_OPTIONS,
  Hotels_SUB_UNIT_OPTIONS,
  Rental_UNIT_VIEW_TYPES_OPTIONS,
  RENTAL_FINISHING_STATUS_TYPES_OPTIONS,
  RENT_DURATION_OPTIONS,
  PURCHASE_VACATION_UNIT_VIEW_TYPES_OPTIONS,
  PURCHASE_FINISHING_STATUS_TYPES_OPTIONS,
  PHARMACY_FINISHING_STATUS_TYPES_OPTIONS,
  Purchase_OTHER_ACCESSORIES_OPTIONS,
  PURCHASE_UNIT_VIEW_TYPES_OPTIONS,
  ALL_FURNISHING_STATUS_OPTIONS,
  BUILD_RENT_FINISHING_STATUS_TYPES_OPTIONS,
  SPECIAL_Hotels_SUB_UNIT_OPTIONS,
  RENTALIN_FINISHING_STATUS_TYPES_OPTIONS,
  Special_RENTAL_FINISHING_STATUS_TYPES_OPTIONS,
  Specific_Rental_UNIT_VIEW_TYPES_OPTIONS,
  COMPANY_Special_RENTAL_FINISHING_STATUS_TYPES_OPTIONS,
} from '../../stepper-modal.constants';

@Injectable({
  providedIn: 'root'
})
export class RentalConfigService extends BaseConfigService {

  // ============================================================================
  // RENT-IN CONFIGURATIONS (Tenant looking for property)
  // ============================================================================

  /**
   * Create rental-specific location inputs for rent-in scenarios
   */
  private createRentInLocationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 2,
        name: 'compoundName',
        type: 'text',
        label: 'Preferred Compound Name',
        validators: [],
        visibility: () => stepperModal.getInsideCompoundPrivilege(),
      },
      {
        step: 2,
        name: 'locationSuggestions',
        type: 'checkbox',
        label: 'Location Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 2,
        name: 'cityId',
        type: 'select',
        label: 'Preferred City',
        options: [],
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'areaId',
        type: 'select',
        label: 'Preferred Area',
        options: [],
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'subAreaId',
        type: 'select',
        label: 'Preferred Sub Area',
        options: [],
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create rent-in unit information inputs
   */
  private createRentInUnitInformationInputs(stepperModal: any, includeRooms: boolean = true): InputConfig[] {
    const baseInputs: InputConfig[] = [
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Favorite Floor',
        options: FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Minimum Unit Area (m²)',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Maximum Unit Area (m²)',
        validators: [Validators.required],
        visibility: () => true,
      },
    ];

    const roomInputs: InputConfig[] = includeRooms ? [
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.required],
        visibility: () => true,
      },
    ] : [];

    const commonInputs: InputConfig[] = [
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Preferred View',
        options: Rental_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Preferred Finishing Status',
        options: RENTAL_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Preferred Furnishing Status',
        options: ALL_FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Desired Accessories',
        options: OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Additional Requirements',
        validators: [],
        visibility: () => true,
      },
    ];

    return [...baseInputs, ...roomInputs, ...commonInputs];
  }

  /**
   * Create rent-in penthouses unit information inputs
   */
  private createRentInPenthousesUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Minimum Unit Area (m²)',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Maximum Unit Area (m²)',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Preferred View',
        options: Specific_Rental_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Preferred Finishing Status',
        options: RENTALIN_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Preferred Furnishing Status',
        options: ALL_FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Desired Accessories',
        options: OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Additional Requirements',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create rent-in villas unit information inputs
   */
  private createRentInVillasUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'numberOfFloors',
        type: 'number',
        label: 'Number of Floors',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Minimum Unit Area (m²)',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Maximum Unit Area (m²)',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Preferred View',
        options: Specific_Rental_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Preferred Finishing Status',
        options: COMPANY_Special_RENTAL_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Preferred Furnishing Status',
        options: ALL_FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Desired Accessories',
        options: OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Additional Requirements',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create rent-in house unit information inputs (for twin houses, town houses, standalone villas)
   */
  private createRentInHouseUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'numberOfFloors',
        type: 'number',
        label: 'Number of Floors',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Minimum Unit Area (m²)',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Maximum Unit Area (m²)',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'groundAreaMin',
        type: 'number',
        label: 'Minimum Ground Area (m²)',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'groundAreaMax',
        type: 'number',
        label: 'Maximum Ground Area (m²)',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Preferred View',
        options: Specific_Rental_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Preferred Finishing Status',
        options: COMPANY_Special_RENTAL_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Preferred Furnishing Status',
        options: ALL_FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Desired Accessories',
        options: OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Additional Requirements',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create rent-in commercial unit information inputs (for administrative units, medical clinics)
   */
  private createRentInCommercialUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
       {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Favorite Floor',
        options: FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Minimum Unit Area (m²)',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Maximum Unit Area (m²)',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Preferred View',
        options: Specific_Rental_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Preferred Finishing Status',
        options: COMPANY_Special_RENTAL_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Preferred Furnishing Status',
        options: ALL_FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Desired Accessories',
        options: Purchase_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Additional Requirements',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create rent-in pharmacies unit information inputs
   */
  private createRentInPharmaciesUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Favorite Floor',
        options: FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Minimum Unit Area (m²)',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Maximum Unit Area (m²)',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Preferred View',
        options: Specific_Rental_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Preferred Finishing Status',
        options: BUILD_RENT_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'fitOutCondition',
        type: 'select',
        label: 'Preferred Fit-Out Condition',
        options: FIT_OUT_CONDITION_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Desired Accessories',
        options: OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Additional Requirements',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create rent-in commercial stores unit information inputs
   */
  private createRentInCommercialStoresUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Favorite Floor',
        options: FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Minimum Unit Area (m²)',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Maximum Unit Area (m²)',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Preferred View',
        options: Specific_Rental_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Preferred Finishing Status',
        options: BUILD_RENT_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'fitOutCondition',
        type: 'select',
        label: 'Preferred Fit-Out Condition',
        options: FIT_OUT_CONDITION_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'shopActivity',
        type: 'text',
        label: 'Activity',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Desired Accessories',
        options: Purchase_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Additional Requirements',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create rent-in commercial administrative buildings unit information inputs
   */
  private createRentInCommercialAdministrativeBuildingsUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitAreaMin',
        type: 'number',
        label: 'Minimum building Area (m²)',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitAreaMax',
        type: 'number',
        label: 'Maximum building Area (m²)',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Preferred View',
        options: Specific_Rental_UNIT_VIEW_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'numberOfFloors',
        type: 'number',
        label: 'Number of Floors',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: BUILD_RENT_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'activity',
        type: 'select',
        label: 'Activity Status',
        options: ACTIVITY_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Desired Accessories',
        options: Purchase_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Additional Requirements',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  private createRentInVacationVillasUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
         {
           step: 3,
           name: 'villageName',
           type: 'text',
           label: 'Village Name',
           validators: [Validators.required],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'subUnitType',
           type: 'select',
           label: 'SubUnit Type',
           options: SUB_UNIT_OPTIONS,
           validators: [Validators.required],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'rentDuration',
           type: 'select',
           label: 'Rent Duration',
           options: RENT_DURATION_OPTIONS,
           validators: [Validators.required],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'rentDateMin',
           type: 'date',
           label: 'Rent Date Min',
           validators: [],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'rentDateMax',
           type: 'date',
           label: 'Rent Date Max',
           validators: [],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'rooms',
           type: 'number',
           label: 'Number of Rooms',
           validators: [Validators.min(0)],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'bathRooms',
           type: 'number',
           label: 'Number of Bathrooms',
           validators: [Validators.min(0)],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'floor',
           type: 'select',
           label: 'Floor',
           options: FLOOR_TYPES_OPTIONS,
           validators: [Validators.required],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'unitView',
           type: 'select',
           label: 'View',
           options: PURCHASE_VACATION_UNIT_VIEW_TYPES_OPTIONS,
           validators: [],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'finishingStatus',
           type: 'select',
           label: 'Finishing Status',
           options: PURCHASE_FINISHING_STATUS_TYPES_OPTIONS,
           validators: [Validators.required],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'furnishingStatus',
           type: 'select',
           label: 'Furnishing Status',
           options: ALL_FURNISHING_STATUS_OPTIONS,
           validators: [Validators.required],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'notes',
           type: 'textarea',
           label: 'Notes',
           validators: [],
           visibility: () => true,
         },
       ];
  }
  private createRentInHotelsUnitInformationInputs(stepperModal: any): InputConfig[] {
     return [
         {
           step: 3,
           name: 'subUnitType',
           type: 'select',
           label: 'SubUnit Type',
           options: SPECIAL_Hotels_SUB_UNIT_OPTIONS,
           validators: [Validators.required],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'rentDuration',
           type: 'select',
           label: 'Rent Duration',
           options: RENT_DURATION_OPTIONS,
           validators: [Validators.required],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'rentDateMin',
           type: 'date',
           label: 'Rent Date Min',
           validators: [],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'rentDateMax',
           type: 'date',
           label: 'Rent Date Max',
           validators: [],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'rooms',
           type: 'number',
           label: 'Number of Rooms',
           validators: [Validators.min(0)],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'bathRooms',
           type: 'number',
           label: 'Number of Bathrooms',
           validators: [Validators.min(0)],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'floor',
           type: 'select',
           label: 'Floor',
           options: FLOOR_TYPES_OPTIONS,
           validators: [Validators.required],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'unitView',
           type: 'select',
           label: 'View',
           options: PURCHASE_VACATION_UNIT_VIEW_TYPES_OPTIONS,
           validators: [],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'finishingStatus',
           type: 'select',
           label: 'Finishing Status',
           options: RENTALIN_FINISHING_STATUS_TYPES_OPTIONS,
           validators: [Validators.required],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'furnishingStatus',
           type: 'select',
           label: 'Furnishing Status',
           options: ALL_FURNISHING_STATUS_OPTIONS,
           validators: [Validators.required],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'notes',
           type: 'textarea',
           label: 'Notes',
           validators: [],
           visibility: () => true,
         },
       ];
  }

  /**
   * Create rent-in financial inputs
   */
  private createRentInFinancialInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 5,
        name: 'averageUnitPriceMin',
        type: 'number',
        label: 'Minimum Average Monthly Unit Price',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 5,
        name: 'averageUnitPriceMax',
        type: 'number',
        label: 'Maximum Average Monthly Unit Price',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 5,
        name: 'unitPriceSuggestions',
        type: 'checkbox',
        label: 'Monthly Unit Price Suggestions',
        validators: [],
        visibility: () => true,
      },
      {
        step: 5,
        name: 'rentRecurrence',
        type: 'select',
        label: 'Rent Recurrence',
        options: RENT_RECURRENCE_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
    ];
  }

  private createRentInSpecificFinancialInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 5,
        name: 'averageUnitPriceMonthlyMin',
        type: 'number',
        label: 'Minimum Monthly Average Unit Price',
        validators: [Validators.required],
        visibility: () => stepperModal.isMonthly(),
      },
      {
        step: 5,
        name: 'averageUnitPriceMonthlyMax',
        type: 'number',
        label: 'Maximum Monthly Average Unit Price',
        validators: [Validators.required],
        visibility: () => stepperModal.isMonthly(),
      },
      {
        step: 5,
        name: 'averageUnitPriceMonthlySuggestions',
        type: 'checkbox',
        label: 'Monthly Unit Price Suggestions',
        validators: [],
        visibility: () => stepperModal.isMonthly(),
      },
      {
        step: 5,
        name: 'averageUnitPriceDailyMin',
        type: 'number',
        label: 'Minimum Daily Average Unit Price',
        validators: [Validators.required],
        visibility: () => !stepperModal.isMonthly(),
      },
      {
        step: 5,
        name: 'averageUnitPriceDailyMax',
        type: 'number',
        label: 'Maximum Daily Average Unit Price',
        validators: [Validators.required],
        visibility: () => !stepperModal.isMonthly(),
      },
      {
        step: 5,
        name: 'averageUnitPriceDailySuggestions',
        type: 'checkbox',
        label: 'Daily Unit Price Suggestions',
        validators: [],
        visibility: () => !stepperModal.isMonthly(),
      },

    ];
  }

  // ============================================================================
  // RENT-OUT CONFIGURATIONS
  // ============================================================================

  /**
   * Create rental-specific location inputs for rent-out scenarios
   */
  private createRentOutLocationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 2,
        name: 'compoundName',
        type: 'text',
        label: 'Compound Name',
        validators: [Validators.required],
        visibility: () => stepperModal.getInsideCompoundPrivilege(),
      },
      {
        step: 2,
        name: 'locationSuggestions',
        type: 'checkbox',
        label: 'Location Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      // Rent-out specific location fields
      {
        step: 2,
        name: 'detailedAddress',
        type: 'text',
        label: 'Detailed Address',
        validators: [Validators.required],
        visibility: () => stepperModal.getSellInsideCompoundInputs() || stepperModal.getRentOutInsideCompoundInputs(),
      },
      {
        step: 2,
        name: 'addressLink',
        type: 'url',
        label: 'Address Link',
        validators: [Validators.pattern(/^https?:\/\/.+/)],
        visibility: () => stepperModal.getSellInsideCompoundInputs() || stepperModal.getRentOutInsideCompoundInputs(),
      },
      {
        step: 2,
        name: 'cityId',
        type: 'select',
        label: 'City',
        options: [],
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'areaId',
        type: 'select',
        label: 'Area',
        options: [],
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'subAreaId',
        type: 'select',
        label: 'Sub Area',
        options: [],
        validators: [],
        visibility: () => true,
      },
    ];
  }

  private createRentOutSpecificLocationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 2,
        name: 'compoundName',
        type: 'text',
        label: 'Compound Name',
        validators: [Validators.required],
        visibility: () => stepperModal.getInsideCompoundPrivilege(),
      },
      {
        step: 2,
        name: 'locationSuggestions',
        type: 'checkbox',
        label: 'Location Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      // Rent-out specific location fields
      {
        step: 2,
        name: 'cityId',
        type: 'select',
        label: 'City',
        options: [],
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'areaId',
        type: 'select',
        label: 'Area',
        options: [],
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'subAreaId',
        type: 'select',
        label: 'Sub Area',
        options: [],
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create rent-out unit information inputs
   */
  private createRentOutUnitInformationInputs(stepperModal: any, includeRooms: boolean = true, includeUnitNumber: boolean = true): InputConfig[] {
    const baseInputs: InputConfig[] = [
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor Number',
        options: SELL_FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floorNumber',
        type: 'number',
        label: 'Floor Number',
        validators: [],
        visibility: () => true,
      },
    ];

    // Add unit number for rent-out inside compound (except commercial administrative buildings)
    if (includeUnitNumber) {
      baseInputs.push({
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      });
    }

    const roomInputs: InputConfig[] = includeRooms ? [
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
    ] : [];

    const commonInputs: InputConfig[] = [
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: Special_RENTAL_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Furnishing Status',
        options: FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Available Accessories',
        options: OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Property Description',
        validators: [],
        visibility: () => true,
      },
    ];

    return [...baseInputs, ...roomInputs, ...commonInputs];
  }

  private createRentOutDuplexUnitInformationInputs(stepperModal: any, includeRooms: boolean = true, includeUnitNumber: boolean = true): InputConfig[] {
    const baseInputs: InputConfig[] = [
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'gardenArea',
        type: 'number',
        label: 'garden Area (m²)',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor Number',
        options: SELL_FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floorNumber',
        type: 'number',
        label: 'Floor Number',
        validators: [],
        visibility: () => true,
      },
    ];

    // Add unit number for rent-out inside compound (except commercial administrative buildings)
    if (includeUnitNumber) {
      baseInputs.push({
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      });
    }

    const roomInputs: InputConfig[] = includeRooms ? [
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
    ] : [];

    const commonInputs: InputConfig[] = [
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: RENTAL_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Furnishing Status',
        options: FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Available Accessories',
        options: OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Property Description',
        validators: [],
        visibility: () => true,
      },
    ];

    return [...baseInputs, ...roomInputs, ...commonInputs];
  }

  /**
   * Create rent-out penthouse unit information inputs
   */
  private createRentOutPenthouseUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'terraceArea',
        type: 'number',
        label: 'Terrace Area (m²)',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Furnishing Status',
        options: FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: RENTAL_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Available Accessories',
        options: OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Property Description',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create rent-out villa unit information inputs
   */
  private createRentOutVillaUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'terraceArea',
        type: 'number',
        label: 'Terrace Area (m²)',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'gardenArea',
        type: 'number',
        label: 'Garden Area (m²)',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'numberOfFloors',
        type: 'number',
        label: 'Number of Floors',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Furnishing Status',
        options: FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: RENTAL_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Available Accessories',
        options: OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Property Description',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create rent-out house unit information inputs (for twin houses, town houses, standalone villas)
   */
  private createRentOutHouseUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'groundArea',
        type: 'number',
        label: 'Ground Area (m²)',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'numberOfFloors',
        type: 'number',
        label: 'Number of Floors',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'bathRooms',
        type: 'number',
        label: 'Number of Bathrooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'rooms',
        type: 'number',
        label: 'Number of Rooms',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Furnishing Status',
        options: FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: RENTAL_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Available Accessories',
        options: OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Property Description',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create rent-out commercial unit information inputs (for administrative units, medical clinics)
   */
  private createRentOutCommercialUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: SELL_FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floorNumber',
        type: 'number',
        label: 'Floor Number',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'furnishingStatus',
        type: 'select',
        label: 'Furnishing Status',
        options: FURNISHING_STATUS_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: RENTAL_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Available Accessories',
        options: Purchase_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Property Description',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create rent-out pharmacy unit information inputs
   */
  private createRentOutPharmacyUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: SELL_FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floorNumber',
        type: 'number',
        label: 'Floor Number',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'fitOutCondition',
        type: 'select',
        label: 'Fit-Out Condition',
        options: FIT_OUT_CONDITION_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: PHARMACY_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Available Accessories',
        options: Purchase_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Property Description',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create rent-out commercial store unit information inputs
   */
  private createRentOutCommercialStoreUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: SELL_FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floorNumber',
        type: 'number',
        label: 'Floor Number',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Unit View',
        options: UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'fitOutCondition',
        type: 'select',
        label: 'Fit-Out Condition',
        options: FIT_OUT_CONDITION_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'shopActivity',
        type: 'text',
        label: 'Activity',
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: PHARMACY_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Available Accessories',
        options: Purchase_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Property Description',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create rent-out commercial administrative building unit information inputs
   */
  private createRentOutCommercialAdministrativeBuildingUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'numberOfFloors',
        type: 'number',
        label: 'Number of Floors',
        validators: [Validators.required, Validators.min(1)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Building Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'Building View',
        options: UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'activity',
        type: 'select',
        label: 'Activity Type',
        options: ACTIVITY_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: PHARMACY_FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Available Accessories',
        options: Purchase_OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Property Description',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  private createRentOutVacationVillasAndChaletsUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
         {
          step: 3,
          name: 'villageName',
          type: 'text',
          label: 'Village Name',
          validators: [Validators.required],
          visibility: () => true,
         },
         {
          step: 3,
          name: 'subUnitType',
          type: 'select',
          label: 'SubUnit Type',
          options: SUB_UNIT_OPTIONS,
          validators: [Validators.required],
          visibility: () => true,
         },
         {
          step: 3,
          name: 'unitNumber',
          type: 'text',
          label: 'Unit Number',
          validators: [Validators.required],
          visibility: () => true,
         },
         {
           step: 3,
           name: 'buildingNumber',
           type: 'text',
           label: 'Building Number',
           validators: [Validators.required],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'unitArea',
           type: 'number',
           label: 'Unit Area (m²)',
           validators: [Validators.required, Validators.min(0)],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'rooms',
           type: 'number',
           label: 'Number of Rooms',
           validators: [Validators.required, Validators.min(0)],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'bathRooms',
           type: 'number',
           label: 'Number of Bathrooms',
           validators: [Validators.required, Validators.min(0)],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'floor',
           type: 'select',
           label: 'Floor',
           options: SELL_FLOOR_TYPES_OPTIONS,
           validators: [Validators.required],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'floorNumber',
           type: 'number',
           label: 'Floor Number',
           validators: [],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'unitView',
           type: 'select',
           label: 'Unit View',
           options: SELL_VACATION_UNIT_VIEW_TYPES_OPTIONS,
           validators: [Validators.required],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'unitFacing',
           type: 'select',
           label: 'Unit Facing',
           options: UNIT_FACING_TYPES_OPTIONS,
           validators: [Validators.required],
           visibility: () => true,
         },
          {
           step: 3,
           name: 'finishingStatus',
           type: 'select',
           label: 'Finishing Status',
           options: RENTAL_FINISHING_STATUS_TYPES_OPTIONS,
           validators: [Validators.required],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'furnishingStatus',
           type: 'select',
           label: 'Furnishing Status',
           options: FURNISHING_STATUS_OPTIONS,
           validators: [Validators.required],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'otherAccessories',
           type: 'multiSelect',
           label: 'Available Accessories',
           options: SPECIFIC_OTHER_ACCESSORIES_OPTIONS,
           validators: [],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'notes',
           type: 'textarea',
           label: 'Property Description',
           validators: [],
           visibility: () => true,
         },
       ];
  }

  private createRentOutHotelsUnitInformationInputs(stepperModal: any): InputConfig[] {
    return [
         {
           step: 3,
           name: 'subUnitType',
           type: 'select',
           label: 'SubUnit Type',
           options: Hotels_SUB_UNIT_OPTIONS,
           validators: [Validators.required],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'unitNumber',
           type: 'text',
           label: 'Unit Number',
           validators: [Validators.required],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'buildingNumber',
           type: 'text',
           label: 'Building Number',
           validators: [Validators.required],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'unitArea',
           type: 'number',
           label: 'Unit Area (m²)',
           validators: [Validators.required, Validators.min(0)],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'rooms',
           type: 'number',
           label: 'Number of Rooms',
           validators: [Validators.required, Validators.min(0)],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'bathRooms',
           type: 'number',
           label: 'Number of Bathrooms',
           validators: [Validators.required, Validators.min(0)],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'floor',
           type: 'select',
           label: 'Floor',
           options: SELL_FLOOR_TYPES_OPTIONS,
           validators: [Validators.required],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'floorNumber',
           type: 'number',
           label: 'Floor Number',
           validators: [],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'unitView',
           type: 'select',
           label: 'Unit View',
           options: Rental_UNIT_VIEW_TYPES_OPTIONS,
           validators: [Validators.required],
           visibility: () => true,
         },
          {
           step: 3,
           name: 'finishingStatus',
           type: 'select',
           label: 'Finishing Status',
           options: RENTAL_FINISHING_STATUS_TYPES_OPTIONS,
           validators: [Validators.required],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'furnishingStatus',
           type: 'select',
           label: 'Furnishing Status',
           options: FURNISHING_STATUS_OPTIONS,
           validators: [Validators.required],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'otherAccessories',
           type: 'multiSelect',
           label: 'Available Accessories',
           options: OTHER_ACCESSORIES_OPTIONS,
           validators: [],
           visibility: () => true,
         },
         {
           step: 3,
           name: 'notes',
           type: 'textarea',
           label: 'Property Description',
           validators: [],
           visibility: () => true,
         },
       ];
  }

  /**
   * Create rent-out financial inputs
   */
  private createRentOutFinancialInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 5,
        name: 'unitPrice',
        type: 'number',
        label: 'Unit Monthly Rent',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 5,
        name: 'unitPriceSuggestions',
        type: 'checkbox',
        label: 'Rent Price Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 5,
        name: 'rentRecurrence',
        type: 'select',
        label: 'Rent Recurrence',
        options: RENT_RECURRENCE_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 5,
        name: 'requiredInsurance',
        type: 'select',
        label: 'Required Insurance',
        options: REQUIRED_INSURANCE_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 5,
        name: 'requiredInsuranceValue',
        type: 'text',
        label: 'Required Insurance Value',
        validators: [],
        visibility: () => true,
      },
      {
        step: 5,
        name: 'otherExpenses',
        type: 'multiSelect',
        label: 'Other Expenses (Tenant Responsibility)',
        options: OTHER_EXPENSES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
    ];
  }
  private createRentOutSpecificFinancialInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 5,
        name: 'unitPriceMonthly',
        type: 'number',
        label: 'Monthly Unit Price',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 5,
        name: 'UnitPriceMonthlySuggestions',
        type: 'checkbox',
        label: 'Monthly Price Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
       {
        step: 5,
        name: 'unitPriceDaily',
        type: 'number',
        label: 'Daily Unit Price',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 5,
        name: 'UnitPriceDailySuggestions',
        type: 'checkbox',
        label: 'Daily Price Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
    ];
  }

  /**
   * Create media inputs for step 4
   */
  private createMediaInputs(): InputConfig[] {
    return [
      {
        step: 4,
        name: 'mainImage',
        type: 'file',
        label: 'Main Image',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 4,
        name: 'galleryImages',
        type: 'file',
        label: 'Gallery Images',
        validators: [],
        visibility: () => true,
      },
      {
        step: 4,
        name: 'unitInMasterPlanImage',
        type: 'file',
        label: 'Unit in Master Plan Image',
        validators: [],
        visibility: () => true,
      },
      {
        step: 4,
        name: 'video',
        type: 'file',
        label: 'Video',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  // ============================================================================
  // CONFIGURATION BUILDERS
  // ============================================================================

  /**
   * Generic rent-in configuration builder
   */
  private createRentInConfig(
    unitType: string,
    stepperModal: any,
    options: { includeRooms?: boolean; includeDocuments?: boolean } = {}
  ): InputConfig[] {
    const { includeRooms = true, includeDocuments = false } = options;

    const config: InputConfig[] = [
      ...this.createRentInLocationInputs(stepperModal),
      ...this.createRentInUnitInformationInputs(stepperModal, includeRooms),
    ];

    // Add documents if needed
    if (includeDocuments) {
      config.push(...this.createDocumentInputs());
    }

    // Add financial inputs
    config.push(...this.createRentInFinancialInputs(stepperModal));

    return config;
  }

  /**
   * Generic rent-out configuration builder
   */
  private createRentOutConfig(
    unitType: string,
    stepperModal: any,
    options: { includeRooms?: boolean; includeMedia?: boolean; includeUnitNumber?: boolean } = {}
  ): InputConfig[] {
    const { includeRooms = true, includeMedia = true, includeUnitNumber = true } = options;

    const config: InputConfig[] = [
      ...this.createRentOutLocationInputs(stepperModal),
      ...this.createRentOutUnitInformationInputs(stepperModal, includeRooms, includeUnitNumber),
    ];

    // Add media inputs if needed
    if (includeMedia) {
      config.push(...this.createMediaInputs());
    }

    // Add financial inputs
    config.push(...this.createRentOutFinancialInputs(stepperModal));

    return config;
  }

  private createRentOutDuplexConfig(
    unitType: string,
    stepperModal: any,
    options: { includeRooms?: boolean; includeMedia?: boolean; includeUnitNumber?: boolean } = {}
  ): InputConfig[] {
    const { includeRooms = true, includeMedia = true, includeUnitNumber = true } = options;

    const config: InputConfig[] = [
      ...this.createRentOutLocationInputs(stepperModal),
      ...this.createRentOutDuplexUnitInformationInputs(stepperModal, includeRooms, includeUnitNumber),
    ];

    // Add media inputs if needed
    if (includeMedia) {
      config.push(...this.createMediaInputs());
    }

    // Add financial inputs
    config.push(...this.createRentOutFinancialInputs(stepperModal));

    return config;
  }

  // ============================================================================
  // SPECIFIC CONFIGURATIONS (Ready for manual modification)
  // ============================================================================

  // RENT-IN CONFIGURATIONS
  private createRentInApartmentsConfig(stepperModal: any): InputConfig[] {
    return this.createRentInConfig('apartments', stepperModal, {
      includeRooms: true,
      includeDocuments: false
    });
  }

  private createRentInDuplexesConfig(stepperModal: any): InputConfig[] {
    return this.createRentInConfig('duplexes', stepperModal, {
      includeRooms: true,
      includeDocuments: false
    });
  }

  private createRentInStudiosConfig(stepperModal: any): InputConfig[] {
    return this.createRentInConfig('studios', stepperModal, {
      includeRooms: false,
      includeDocuments: false
    });
  }

  private createRentInVillasConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentInLocationInputs(stepperModal),
      ...this.createRentInVillasUnitInformationInputs(stepperModal),
    ];

    // Add financial inputs for step 5
    config.push(...this.createRentInFinancialInputs(stepperModal));

    return config;
  }

  private createRentInPenthousesConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentInLocationInputs(stepperModal),
      ...this.createRentInPenthousesUnitInformationInputs(stepperModal),
    ];

    // Add financial inputs for step 5
    config.push(...this.createRentInFinancialInputs(stepperModal));

    return config;
  }

  private createRentInTwinHousesConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentInLocationInputs(stepperModal),
      ...this.createRentInHouseUnitInformationInputs(stepperModal),
    ];

    // Add financial inputs for step 5
    config.push(...this.createRentInFinancialInputs(stepperModal));

    return config;
  }

  private createRentInTownHousesConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentInLocationInputs(stepperModal),
      ...this.createRentInHouseUnitInformationInputs(stepperModal),
    ];

    // Add financial inputs for step 5
    config.push(...this.createRentInFinancialInputs(stepperModal));

    return config;
  }

  private createRentInStandaloneVillasConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentInLocationInputs(stepperModal),
      ...this.createRentInHouseUnitInformationInputs(stepperModal),
    ];

    // Add financial inputs for step 5
    config.push(...this.createRentInFinancialInputs(stepperModal));

    return config;
  }

  private createRentInAdministrativeUnitsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentInLocationInputs(stepperModal),
      ...this.createRentInCommercialUnitInformationInputs(stepperModal),
    ];

    // Add financial inputs for step 5
    config.push(...this.createRentInFinancialInputs(stepperModal));

    return config;
  }

  private createRentInMedicalClinicsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentInLocationInputs(stepperModal),
      ...this.createRentInCommercialUnitInformationInputs(stepperModal),
    ];

    // Add financial inputs for step 5
    config.push(...this.createRentInFinancialInputs(stepperModal));

    return config;
  }

  private createRentInPharmaciesConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentInLocationInputs(stepperModal),
      ...this.createRentInPharmaciesUnitInformationInputs(stepperModal),
    ];

    // Add financial inputs for step 5
    config.push(...this.createRentInFinancialInputs(stepperModal));

    return config;
  }

  private createRentInCommercialStoresConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentInLocationInputs(stepperModal),
      ...this.createRentInCommercialStoresUnitInformationInputs(stepperModal),
    ];

    // Add financial inputs for step 5
    config.push(...this.createRentInFinancialInputs(stepperModal));

    return config;
  }

  private createRentInCommercialAdministrativeBuildingsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentInLocationInputs(stepperModal),
      ...this.createRentInCommercialAdministrativeBuildingsUnitInformationInputs(stepperModal),
    ];

    // Add financial inputs for step 5
    config.push(...this.createRentInFinancialInputs(stepperModal));

    return config;
  }

  private createRentInVacationVillasAndChaletsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentInLocationInputs(stepperModal),
      ...this.createRentInVacationVillasUnitInformationInputs(stepperModal),
    ];

    // Add financial inputs for step 5
    config.push(...this.createRentInSpecificFinancialInputs(stepperModal));

    return config;
  }

  private createRentInHotelsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentInLocationInputs(stepperModal),
      ...this.createRentInHotelsUnitInformationInputs(stepperModal),
    ];

    // Add financial inputs for step 5
    config.push(...this.createRentInSpecificFinancialInputs(stepperModal));

    return config;
  }

  // RENT-OUT CONFIGURATIONS
  private createRentOutApartmentsConfig(stepperModal: any): InputConfig[] {
    return this.createRentOutConfig('apartments', stepperModal, {
      includeRooms: true,
      includeMedia: true,
      includeUnitNumber: true // Include unit number for apartments
    });
  }

  private createRentOutDuplexesConfig(stepperModal: any): InputConfig[] {
    return this.createRentOutDuplexConfig('duplexes', stepperModal, {
      includeRooms: true,
      includeMedia: true,
      includeUnitNumber: true // Include unit number for duplexes
    });
  }

  private createRentOutStudiosConfig(stepperModal: any): InputConfig[] {
    return this.createRentOutConfig('studios', stepperModal, {
      includeRooms: false,
      includeMedia: true,
      includeUnitNumber: true // Include unit number for studios
    });
  }

  private createRentOutVillasConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentOutLocationInputs(stepperModal),
      ...this.createRentOutVillaUnitInformationInputs(stepperModal),
    ];

    // Add media inputs for step 4
    config.push(...this.createMediaInputs());

    // Add financial inputs for step 5
    config.push(...this.createRentOutFinancialInputs(stepperModal));

    return config;
  }

  private createRentOutPenthousesConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentOutLocationInputs(stepperModal),
      ...this.createRentOutPenthouseUnitInformationInputs(stepperModal),
    ];

    // Add media inputs for step 4
    config.push(...this.createMediaInputs());

    // Add financial inputs for step 5
    config.push(...this.createRentOutFinancialInputs(stepperModal));

    return config;
  }

  private createRentOutTwinHousesConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentOutLocationInputs(stepperModal),
      ...this.createRentOutHouseUnitInformationInputs(stepperModal),
    ];

    // Add media inputs for step 4
    config.push(...this.createMediaInputs());

    // Add financial inputs for step 5
    config.push(...this.createRentOutFinancialInputs(stepperModal));

    return config;
  }

  private createRentOutTownHousesConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentOutLocationInputs(stepperModal),
      ...this.createRentOutHouseUnitInformationInputs(stepperModal),
    ];

    // Add media inputs for step 4
    config.push(...this.createMediaInputs());

    // Add financial inputs for step 5
    config.push(...this.createRentOutFinancialInputs(stepperModal));

    return config;
  }

  private createRentOutStandaloneVillasConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentOutLocationInputs(stepperModal),
      ...this.createRentOutHouseUnitInformationInputs(stepperModal),
    ];

    // Add media inputs for step 4
    config.push(...this.createMediaInputs());

    // Add financial inputs for step 5
    config.push(...this.createRentOutFinancialInputs(stepperModal));

    return config;
  }

  private createRentOutAdministrativeUnitsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentOutLocationInputs(stepperModal),
      ...this.createRentOutCommercialUnitInformationInputs(stepperModal),
    ];

    // Add media inputs for step 4
    config.push(...this.createMediaInputs());

    // Add financial inputs for step 5
    config.push(...this.createRentOutFinancialInputs(stepperModal));

    return config;
  }

  private createRentOutMedicalClinicsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentOutLocationInputs(stepperModal),
      ...this.createRentOutCommercialUnitInformationInputs(stepperModal),
    ];

    // Add media inputs for step 4
    config.push(...this.createMediaInputs());

    // Add financial inputs for step 5
    config.push(...this.createRentOutFinancialInputs(stepperModal));

    return config;
  }

  private createRentOutPharmaciesConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentOutLocationInputs(stepperModal),
      ...this.createRentOutPharmacyUnitInformationInputs(stepperModal),
    ];

    // Add media inputs for step 4
    config.push(...this.createMediaInputs());

    // Add financial inputs for step 5
    config.push(...this.createRentOutFinancialInputs(stepperModal));

    return config;
  }

  private createRentOutCommercialStoresConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentOutLocationInputs(stepperModal),
      ...this.createRentOutCommercialStoreUnitInformationInputs(stepperModal),
    ];

    // Add media inputs for step 4
    config.push(...this.createMediaInputs());

    // Add financial inputs for step 5
    config.push(...this.createRentOutFinancialInputs(stepperModal));

    return config;
  }

  // Commercial configurations (for future use) - exclude unit number
  private createRentOutCommercialAdministrativeBuildingsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentOutLocationInputs(stepperModal),
      ...this.createRentOutCommercialAdministrativeBuildingUnitInformationInputs(stepperModal),
    ];

    // Add media inputs for step 4
    config.push(...this.createMediaInputs());

    // Add financial inputs for step 5
    config.push(...this.createRentOutFinancialInputs(stepperModal));

    return config;
  }

  private createRentOutVacationVillasAndChaletsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentOutSpecificLocationInputs(stepperModal),
      ...this.createRentOutVacationVillasAndChaletsUnitInformationInputs(stepperModal),
    ];

    // Add media inputs for step 4
    config.push(...this.createMediaInputs());

    // Add financial inputs for step 5
    config.push(...this.createRentOutSpecificFinancialInputs(stepperModal));

    return config;
  }

  private createRentOutHotelsConfig(stepperModal: any): InputConfig[] {
    const config: InputConfig[] = [
      ...this.createRentOutSpecificLocationInputs(stepperModal),
      ...this.createRentOutHotelsUnitInformationInputs(stepperModal),
    ];

    // Add media inputs for step 4
    config.push(...this.createMediaInputs());

    // Add financial inputs for step 5
    config.push(...this.createRentOutSpecificFinancialInputs(stepperModal));

    return config;
  }

  // ============================================================================
  // PUBLIC API
  // ============================================================================

  /**
   * Get input configurations for rental cases
   */
  getInputConfigs(stepperModal: any): StepperConfiguration[] {
    return [
      // RENT-IN CONFIGURATIONS
      {
        key: 'rentals_inside_compound_rent_in_apartments',
        value: this.createRentInApartmentsConfig(stepperModal),
      },
      {
        key: 'rentals_inside_compound_rent_in_duplexes',
        value: this.createRentInDuplexesConfig(stepperModal),
      },
      {
        key: 'rentals_inside_compound_rent_in_studios',
        value: this.createRentInStudiosConfig(stepperModal),
      },
      {
        key: 'rentals_inside_compound_rent_in_villas',
        value: this.createRentInVillasConfig(stepperModal),
      },
      {
        key: 'rentals_inside_compound_rent_in_penthouses',
        value: this.createRentInPenthousesConfig(stepperModal),
      },
      {
        key: 'rentals_inside_compound_rent_in_twin_houses',
        value: this.createRentInTwinHousesConfig(stepperModal),
      },
      {
        key: 'rentals_inside_compound_rent_in_town_houses',
        value: this.createRentInTownHousesConfig(stepperModal),
      },
      {
        key: 'rentals_inside_compound_rent_in_standalone_villas',
        value: this.createRentInStandaloneVillasConfig(stepperModal),
      },
      {
        key: 'rentals_inside_compound_rent_in_administrative_units',
        value: this.createRentInAdministrativeUnitsConfig(stepperModal),
      },
      {
        key: 'rentals_inside_compound_rent_in_medical_clinics',
        value: this.createRentInMedicalClinicsConfig(stepperModal),
      },
      {
        key: 'rentals_inside_compound_rent_in_pharmacies',
        value: this.createRentInPharmaciesConfig(stepperModal),
      },
      {
        key: 'rentals_inside_compound_rent_in_commercial_stores',
        value: this.createRentInCommercialStoresConfig(stepperModal),
      },
      {
        key: 'rentals_inside_compound_rent_in_commercial_administrative_buildings',
        value: this.createRentInCommercialAdministrativeBuildingsConfig(stepperModal),
      },
      {
        key: 'rentals_inside_compound_rent_in_vacation_villa',
        value: this.createRentInVacationVillasAndChaletsConfig(stepperModal),
      },
      {
        key: 'rentals_inside_compound_rent_in_chalets',
        value: this.createRentInVacationVillasAndChaletsConfig(stepperModal),
      },
      {
        key: 'rentals_inside_compound_rent_in_hotels',
        value: this.createRentInHotelsConfig(stepperModal),
      },

      // RENT-OUT CONFIGURATIONS
      {
        key: 'rentals_inside_compound_rent_out_apartments',
        value: this.createRentOutApartmentsConfig(stepperModal),
      },
      {
        key: 'rentals_inside_compound_rent_out_duplexes',
        value: this.createRentOutDuplexesConfig(stepperModal),
      },
      {
        key: 'rentals_inside_compound_rent_out_studios',
        value: this.createRentOutStudiosConfig(stepperModal),
      },
      {
        key: 'rentals_inside_compound_rent_out_villas',
        value: this.createRentOutVillasConfig(stepperModal),
      },
      {
        key: 'rentals_inside_compound_rent_out_penthouses',
        value: this.createRentOutPenthousesConfig(stepperModal),
      },
      {
        key: 'rentals_inside_compound_rent_out_twin_houses',
        value: this.createRentOutTwinHousesConfig(stepperModal),
      },
      {
        key: 'rentals_inside_compound_rent_out_town_houses',
        value: this.createRentOutTownHousesConfig(stepperModal),
      },
      {
        key: 'rentals_inside_compound_rent_out_standalone_villas',
        value: this.createRentOutStandaloneVillasConfig(stepperModal),
      },
      {
        key: 'rentals_inside_compound_rent_out_administrative_units',
        value: this.createRentOutAdministrativeUnitsConfig(stepperModal),
      },
      {
        key: 'rentals_inside_compound_rent_out_medical_clinics',
        value: this.createRentOutMedicalClinicsConfig(stepperModal),
      },
      {
        key: 'rentals_inside_compound_rent_out_pharmacies',
        value: this.createRentOutPharmaciesConfig(stepperModal),
      },
      {
        key: 'rentals_inside_compound_rent_out_commercial_stores',
        value: this.createRentOutCommercialStoresConfig(stepperModal),
      },
      {
        key: 'rentals_inside_compound_rent_out_commercial_administrative_buildings',
        value: this.createRentOutCommercialAdministrativeBuildingsConfig(stepperModal),
      },
      {
        key: 'rentals_inside_compound_rent_out_vacation_villa',
        value: this.createRentOutVacationVillasAndChaletsConfig(stepperModal),
      },
      {
        key: 'rentals_inside_compound_rent_out_chalets',
        value: this.createRentOutVacationVillasAndChaletsConfig(stepperModal),
      },
      {
        key: 'rentals_inside_compound_rent_out_hotels',
        value: this.createRentOutHotelsConfig(stepperModal),
      },
    ];
  }

  /**
   * Get all available rental configuration keys
   */
  getRentalConfigurationKeys(): string[] {
    return [
      // RENT-IN KEYS
      'rentals_inside_compound_rent_in_apartments',
      'rentals_inside_compound_rent_in_duplexes',
      'rentals_inside_compound_rent_in_studios',
      'rentals_inside_compound_rent_in_villas',
      'rentals_inside_compound_rent_in_penthouses',
      'rentals_inside_compound_rent_in_twin_houses',
      'rentals_inside_compound_rent_in_town_houses',
      'rentals_inside_compound_rent_in_standalone_villas',
      'rentals_inside_compound_rent_in_administrative_units',
      'rentals_inside_compound_rent_in_medical_clinics',
      'rentals_inside_compound_rent_in_pharmacies',
      'rentals_inside_compound_rent_in_commercial_stores',
      'rentals_inside_compound_rent_in_commercial_administrative_buildings',
      'rentals_inside_compound_rent_in_vacation_villa',
      'rentals_inside_compound_rent_in_chalets',
      'rentals_inside_compound_rent_in_hotels',

      // RENT-OUT KEYS
      'rentals_inside_compound_rent_out_apartments',
      'rentals_inside_compound_rent_out_duplexes',
      'rentals_inside_compound_rent_out_studios',
      'rentals_inside_compound_rent_out_villas',
      'rentals_inside_compound_rent_out_penthouses',
      'rentals_inside_compound_rent_out_twin_houses',
      'rentals_inside_compound_rent_out_town_houses',
      'rentals_inside_compound_rent_out_standalone_villas',
      'rentals_inside_compound_rent_out_administrative_units',
      'rentals_inside_compound_rent_out_medical_clinics',
      'rentals_inside_compound_rent_out_pharmacies',
      'rentals_inside_compound_rent_out_commercial_stores',
      'rentals_inside_compound_rent_out_vacation_villa',
      'rentals_inside_compound_rent_out_commercial_administrative_buildings',
      'rentals_inside_compound_rent_out_chalets',
      'rentals_inside_compound_rent_out_hotels',
    ];
  }

  /**
   * Check if a key is rent-in configuration
   */
  isRentInConfiguration(key: string): boolean {
    return key.includes('_rent_in_');
  }

  /**
   * Check if a key is rent-out configuration
   */
  isRentOutConfiguration(key: string): boolean {
    return key.includes('_rent_out_');
  }
}
