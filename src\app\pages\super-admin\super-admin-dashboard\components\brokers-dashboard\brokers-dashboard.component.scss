.text-white-75 {
  color: rgba(255, 255, 255, 0.75) !important;
}

.card {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }
}

.progress {
  background-color: rgba(0, 0, 0, 0.1);
}

.table {
  th {
    border-top: none;
    font-weight: 600;
    color: #5E6278;
    font-size: 0.875rem;
  }

  td {
    border-top: 1px solid #EEF0F8;
    vertical-align: middle;
  }
}

.bg-light-primary,
.bg-light-success,
.bg-light-warning,
.bg-light-info,
.bg-light-danger {
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.02);
  }
}

.rating {
  .fa-star,
  .fa-star-half-alt {
    font-size: 0.875rem;
    margin-right: 2px;
  }
}

.symbol {
  .symbol-label {
    border-radius: 8px;
  }
}

.progress-bar {
  transition: width 0.6s ease;
}

.badge {
  font-size: 0.75rem;
  padding: 0.5rem 0.75rem;
}

// RTL Table Support
.table-rtl {
  direction: rtl;
  text-align: right;

  th, td {
    text-align: right !important;
    padding-right: 1rem;
    padding-left: 0.5rem;
  }

  th:first-child,
  td:first-child {
    padding-right: 1.5rem;
  }

  th:last-child,
  td:last-child {
    padding-left: 1.5rem;
  }
}

.table-ltr {
  direction: ltr;
  text-align: left;

  th, td {
    text-align: left !important;
  }
}

// RTL Card Support
[style*="direction: rtl"] {
  .card-header {
    .d-flex {
      justify-content: flex-end !important;
    }

    .symbol {
      margin-left: 1rem;
      margin-right: 0;
    }
  }

  .table-responsive {
    overflow-x: auto;
    direction: rtl;
  }

  .card-title {
    text-align: right;
  }
}

// Force RTL alignment for Arabic
.justify-content-end-rtl {
  justify-content: flex-end !important;
}

.justify-content-start-ltr {
  justify-content: flex-start !important;
}
