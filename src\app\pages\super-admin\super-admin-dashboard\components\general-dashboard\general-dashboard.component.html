<div class="container-fluid px-4 py-5" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
  <!-- Header -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card border-0 shadow-sm">
        <div class="card-body p-4" [style.overflow]="'visible'">
          <div class="d-flex align-items-center justify-content-between w-100" [style.overflow]="'visible'"
            [style.flex-wrap]="'wrap'" [style.gap]="'1rem'">

            <!-- للعربية: الزر أولاً (أقصى الشمال) -->
            <div *ngIf="translationService.isRTL()" class="order-1">
              <button class="btn btn-primary btn-sm" (click)="refresh()"
                [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                <i class="ki-duotone ki-arrows-circle fs-4 ms-2">
                  <span class="path1"></span>
                  <span class="path2"></span>
                </i>
                {{ 'SUPER_ADMIN.DASHBOARD.GENERAL.REFRESH_DATA' | translate }}
              </button>
            </div>

            <!-- المحتوى -->
            <div [class.text-end]="translationService.isRTL()" [class.text-start]="!translationService.isRTL()"
              [class.order-2]="translationService.isRTL()">
              <div class="d-flex align-items-center mb-2" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'"
                [style.gap]="translationService.isRTL() ? '1rem' : '0.75rem'">
                <div class="symbol symbol-40px" [class.me-4]="!translationService.isRTL()"
                  [class.ms-4]="translationService.isRTL()" [style.flex-shrink]="'0'">
                  <div class="symbol-label bg-light-primary">
                    <i class="ki-duotone ki-chart-simple fs-3 text-primary">
                      <span class="path1"></span>
                      <span class="path2"></span>
                      <span class="path3"></span>
                      <span class="path4"></span>
                    </i>
                  </div>
                </div>
                <div [style.flex]="'1'" [style.min-width]="'0'" [style.overflow]="'visible'">
                  <h1 class="text-gray-900 fw-bold mb-0 fs-2"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.white-space]="'normal'" [style.word-wrap]="'break-word'" [style.overflow]="'visible'"
                    [style.margin-bottom]="'0.25rem'">
                    {{ 'SUPER_ADMIN.DASHBOARD.GENERAL.TITLE' | translate }}
                  </h1>
                  <p class="text-muted mb-0 fs-6"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.white-space]="'normal'" [style.word-wrap]="'break-word'" [style.overflow]="'visible'">
                    {{ 'SUPER_ADMIN.DASHBOARD.GENERAL.DESCRIPTION' | translate }}
                  </p>
                </div>
              </div>
            </div>

            <!-- للإنجليزية: الزر آخراً (اليمين) -->
            <div *ngIf="!translationService.isRTL()">
              <button class="btn btn-primary btn-sm" (click)="refresh()">
                <i class="ki-duotone ki-arrows-circle fs-4 me-2">
                  <span class="path1"></span>
                  <span class="path2"></span>
                </i>
                {{ 'SUPER_ADMIN.DASHBOARD.GENERAL.REFRESH_DATA' | translate }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Statistics Cards -->
  <div class="row g-4 mb-5">
    <div class="col-lg-3 col-md-6">
      <div class="card border-0 shadow-sm h-100">
        <div class="card-body p-4">
          <div class="d-flex align-items-center" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <div class="symbol" [class.symbol-60px]="!translationService.isRTL()"
              [class.symbol-45px]="translationService.isRTL()" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <div class="symbol-label bg-light-primary">
                <i class="ki-duotone ki-profile-circle text-primary" [class.fs-2]="!translationService.isRTL()"
                  [class.fs-3]="translationService.isRTL()">
                  <span class="path1"></span>
                  <span class="path2"></span>
                  <span class="path3"></span>
                </i>
              </div>
            </div>
            <div [class.text-end]="translationService.isRTL()" [class.text-start]="!translationService.isRTL()">
              <div class="fs-2 fw-bold text-gray-900 mb-1">
                {{ formatNumber(totalClients) | arabicNumbers }}
              </div>
              <div class="fs-7 text-muted"
                [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                {{ 'SUPER_ADMIN.DASHBOARD.GENERAL.CLIENTS' | translate }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-3 col-md-6">
      <div class="card border-0 shadow-sm h-100">
        <div class="card-body p-4">
          <div class="d-flex align-items-center" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <div class="symbol" [class.symbol-60px]="!translationService.isRTL()"
              [class.symbol-45px]="translationService.isRTL()" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <div class="symbol-label bg-light-success">
                <i class="ki-duotone ki-user-tick text-success" [class.fs-2]="!translationService.isRTL()"
                  [class.fs-3]="translationService.isRTL()">
                  <span class="path1"></span>
                  <span class="path2"></span>
                  <span class="path3"></span>
                </i>
              </div>
            </div>
            <div [class.text-end]="translationService.isRTL()" [class.text-start]="!translationService.isRTL()">
              <div class="fs-2 fw-bold text-gray-900 mb-1">
                {{ formatNumber(totalBrokers) | arabicNumbers }}
              </div>
              <div class="fs-7 text-muted"
                [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                {{ 'SUPER_ADMIN.DASHBOARD.GENERAL.BROKERS' | translate }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-3 col-md-6">
      <div class="card border-0 shadow-sm h-100">
        <div class="card-body p-4">
          <div class="d-flex align-items-center" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <div class="symbol" [class.symbol-60px]="!translationService.isRTL()"
              [class.symbol-45px]="translationService.isRTL()" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <div class="symbol-label bg-light-info">
                <i class="ki-duotone ki-office-bag text-info" [class.fs-2]="!translationService.isRTL()"
                  [class.fs-3]="translationService.isRTL()">
                  <span class="path1"></span>
                  <span class="path2"></span>
                  <span class="path3"></span>
                  <span class="path4"></span>
                </i>
              </div>
            </div>
            <div [class.text-end]="translationService.isRTL()" [class.text-start]="!translationService.isRTL()">
              <div class="fs-2 fw-bold text-gray-900 mb-1">
                {{ formatNumber(totalDevelopers) | arabicNumbers }}
              </div>
              <div class="fs-7 text-muted"
                [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                {{ 'SUPER_ADMIN.DASHBOARD.GENERAL.DEVELOPERS' | translate }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-3 col-md-6">
      <div class="card border-0 shadow-sm h-100">
        <div class="card-body p-4">
          <div class="d-flex align-items-center" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <div class="symbol" [class.symbol-60px]="!translationService.isRTL()"
              [class.symbol-45px]="translationService.isRTL()" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <div class="symbol-label bg-light-warning">
                <i class="ki-duotone ki-shield-tick text-warning" [class.fs-2]="!translationService.isRTL()"
                  [class.fs-3]="translationService.isRTL()">
                  <span class="path1"></span>
                  <span class="path2"></span>
                  <span class="path3"></span>
                </i>
              </div>
            </div>
            <div [class.text-end]="translationService.isRTL()" [class.text-start]="!translationService.isRTL()">
              <div class="fs-2 fw-bold text-gray-900 mb-1">
                {{ formatNumber(totalAdmins) | arabicNumbers }}
              </div>
              <div class="fs-7 text-muted"
                [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                {{ 'SUPER_ADMIN.DASHBOARD.GENERAL.ADMINS' | translate }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Charts and Statistics Row -->
  <div class="row g-4 mb-5">
    <!-- Most Demanded by Area -->
    <div class="col-lg-6">
      <div class="card border-0 shadow-sm h-100">
        <div class="card-header border-0 py-4" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
          <div class="d-flex align-items-center w-100" [class.flex-row-reverse]="translationService.isRTL()"
            [style.justify-content]="translationService.isRTL() ? 'flex-end' : 'flex-start'">
            <div class="symbol" [class.symbol-40px]="!translationService.isRTL()"
              [class.symbol-35px]="translationService.isRTL()" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <div class="symbol-label bg-light-primary">
                <i class="ki-duotone ki-geolocation text-primary" [class.fs-3]="!translationService.isRTL()"
                  [class.fs-4]="translationService.isRTL()">
                  <span class="path1"></span>
                  <span class="path2"></span>
                </i>
              </div>
            </div>
            <h3 class="card-title text-gray-900 fw-bold fs-4 mb-0"
              [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ 'SUPER_ADMIN.DASHBOARD.GENERAL.MOST_REQUESTED_UNITS' | translate }}
            </h3>
          </div>
        </div>
        <div class="card-body p-4">
          <div class="table-responsive">
            <table class="table table-hover align-middle"
              [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
              <thead class="table-light">
                <tr>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.GENERAL.AREA' | translate }}
                  </th>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.GENERAL.UNIT_TYPE' | translate }}
                  </th>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.GENERAL.COUNT' | translate }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let unit of mostRequested">
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="text-gray-900 fw-semibold fs-6"
                      [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                      {{ unit.area }}
                    </span>
                  </td>
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="text-gray-700 fs-6"
                      [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                      {{ unit.unitType }}
                    </span>
                  </td>
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="badge badge-light-primary fs-7">{{ unit.count }}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Most Sold Units -->
    <div class="col-lg-6">
      <div class="card border-0 shadow-sm h-100">
        <div class="card-header border-0 py-4" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-40px me-3">
              <div class="symbol-label bg-light-success">
                <i class="ki-duotone ki-chart-pie-simple fs-3 text-success">
                  <span class="path1"></span>
                  <span class="path2"></span>
                </i>
              </div>
            </div>
            <h3 class="card-title text-gray-900 fw-bold fs-4 mb-0"
              [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
              {{ 'SUPER_ADMIN.DASHBOARD.GENERAL.MOST_SOLD_UNITS' | translate }}
            </h3>
          </div>
        </div>
        <div class="card-body p-4">
          <div class="table-responsive">
            <table class="table table-hover align-middle"
              [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
              <thead class="table-light">
                <tr>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.GENERAL.AREA' | translate }}
                  </th>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.GENERAL.UNIT_TYPE' | translate }}
                  </th>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.GENERAL.COUNT' | translate }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let unit of mostSold">
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="text-gray-900 fw-semibold fs-6"
                      [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                      {{ unit.area }}
                    </span>
                  </td>
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="text-gray-700 fs-6"
                      [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                      {{ unit.unitType }}
                    </span>
                  </td>
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="badge badge-light-success fs-7">{{ unit.count }}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row g-4 mb-5">
    <!-- Monthly Advertisements -->
    <div class="col-lg-6">
      <div class="card border-0 shadow-sm h-100">
        <div class="card-header border-0 py-4" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-40px me-3">
              <div class="symbol-label bg-light-info">
                <i class="ki-duotone ki-calendar fs-3 text-info">
                  <span class="path1"></span>
                  <span class="path2"></span>
                </i>
              </div>
            </div>
            <h3 class="card-title text-gray-900 fw-bold fs-4 mb-0"
              [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
              {{ 'SUPER_ADMIN.DASHBOARD.GENERAL.MONTHLY_ADVERTISEMENTS' | translate }}
            </h3>
          </div>
        </div>
        <div class="card-body p-4">
          <div class="table-responsive">
            <table class="table table-hover align-middle"
              [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
              <thead class="table-light">
                <tr>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.GENERAL.DATE' | translate }}
                  </th>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.GENERAL.TOTAL_NUMBER' | translate }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let add of monthlyAdvertisement">
                  <td class="ps-4">
                    <span class="text-dark fw-bold">{{ add.date }}</span>
                  </td>
                  <td>
                    <span class="badge badge-light-primary">{{ add.totalNumberOfUnits }}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Most Demanded by Type -->
    <div class="col-lg-6">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-map-marker-alt me-2"></i>
            Registered Areas & Specializations
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="table-responsive">
            <table class="table table-row-bordered align-middle gs-0 gy-3">
              <thead>
                <tr class="fw-bold text-muted bg-light">
                  <th class="ps-4 rounded-start">Area</th>
                  <th class="ps-4 rounded-start">Specialization</th>
                  <th>Count </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let unit of registeredStats">
                  <td class="ps-4">
                    <span class="text-dark fw-bold">{{ unit.area }}</span>
                  </td>
                  <td class="ps-4">
                    <span class="text-dark fw-bold">{{ unit.specialization_scope }}</span>
                  </td>
                  <td>
                    <span class="badge badge-light-primary">{{ unit.count }}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Best Selling Units -->
  <!-- <div class="row g-6 mb-8">
    <div class="col-12">
      <div class="card card-flush">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-trophy me-2"></i>
            الوحدات الأكثر مبيعاً
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="table-responsive">
            <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3">
              <thead>
                <tr class="fw-bold text-muted bg-light">
                  <th class="ps-4 rounded-start">الوحدة</th>
                  <th>السعر</th>
                  <th>المنطقة</th>
                  <th>النوع</th>
                  <th>عدد المبيعات</th>
                  <th class="rounded-end">المطور</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let unit of bestSellingUnits">
                  <td class="ps-4">
                    <span class="text-dark fw-bold">{{ unit.title }}</span>
                  </td>
                  <td>
                    <span class="text-primary fw-bold">{{ formatPrice(unit.price) }}</span>
                  </td>
                  <td>
                    <span class="text-muted">{{ unit.area }}</span>
                  </td>
                  <td>
                    <span class="badge badge-light-info">{{ unit.type }}</span>
                  </td>
                  <td>
                    <span class="badge badge-light-success">{{ unit.sales }}</span>
                  </td>
                  <td>
                    <span class="text-muted">{{ unit.developer }}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div> -->

  <!-- Price Statistics -->
  <div class="row g-6">
    <div class="col-lg-6">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-map me-2"></i>
            Subscriptions Accounts
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="table-responsive">
            <table class="table table-row-bordered align-middle gs-0 gy-3">
              <thead>
                <tr class="fw-bold text-muted bg-light">
                  <th class="ps-4 rounded-start">Account Type</th>
                  <th>Count</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let unit of subscriptionsAccounts">
                  <td class="ps-4">
                    <span class="text-dark fw-bold">{{ unit.accountType }}</span>
                  </td>
                  <td>
                    <span class="text-primary fw-bold">{{ unit.count }}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Area Statistics -->
    <div class="col-lg-6">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-map me-2"></i>
            Avg Unit Price
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="table-responsive">
            <table class="table table-row-bordered align-middle gs-0 gy-3">
              <thead>
                <tr class="fw-bold text-muted bg-light">
                  <th class="ps-4 rounded-start">Area</th>
                  <th>Unit Type</th>
                  <th>Avg Unit price </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let unit of avgUnitPrice">
                  <td class="ps-4">
                    <span class="text-dark fw-bold">{{ unit.area }}</span>
                  </td>
                  <td>
                    <span class="text-muted">{{ unit.unitType }}</span>
                  </td>
                  <td>
                    <span class="text-primary fw-bold">{{ unit.averagePriceInMeterInCash }}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>