import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { UsersService } from '../../../services/users.service';
import { TranslationService } from '../../../../../modules/i18n/translation.service';

@Component({
  selector: 'app-general-dashboard',
  templateUrl: './general-dashboard.component.html',
  styleUrls: ['./general-dashboard.component.scss']
})
export class GeneralDashboardComponent implements OnInit {

  // General statistics
  loading = false;
  totalClients = 0;
  totalBrokers =  0;
  totalDevelopers=  0;
  totalAdmins = 0;
  mostRequested: any[] = [];
  mostSold: any[] = [];
  avgUnitPrice: any[] = [];
  monthlyAdvertisement: any[] = [];
  registeredStats: any[] = [];
  subscriptionsAccounts: any[] = [];

  constructor(
    private cd: ChangeDetectorRef,
    private userService: UsersService,
    public translationService: TranslationService
  ) {}

  ngOnInit() {
    this.loadMostRequestedUnits();
    this.loadMostSoldUnits();
    this.loadMostAreasAndSpecializations();
    this.loadMostSubscriptionAccounts();
    this.loadNoOfUsers();
  }

  loadNoOfUsers() {
    this.loading = true;
    this.userService.loadNoOfClients().subscribe({
      next: (response:any) => {
        console.log(response);
        const roles = response.data;
        const clientData = roles.find((item: any) => item.role === 'client');
        const brokerData = roles.find((item: any) => item.role === 'broker');
        const developerData = roles.find((item: any) => item.role === 'developer');
        const adminData = roles.find((item: any) => item.role === 'admin');
        this.totalClients = clientData ? clientData.count : 0;
        this.totalBrokers = brokerData ? brokerData.count : 0;
        this.totalDevelopers= developerData ? developerData.count : 0;
        this.totalAdmins = adminData ? adminData.count : 0;
        this.cd.detectChanges();
        this.loading = false;
      },
      error: (err:any) => {
        console.error('Failed to load client statistics', err);
        this.loading = false;
      },
    });
  }

  loadMostRequestedUnits() {
    this.loading = true;
    this.userService.loadMostRequestedUnits().subscribe({
      next: (response:any) => {
        console.log(response);
        this.mostRequested = response.data.mostRequestedRequest;
        this.cd.detectChanges();
        this.loading = false;
      },
      error: (err:any) => {
        console.error('Failed to load client statistics', err);
        this.loading = false;
      },
    });
  }

  loadMostAreasAndSpecializations() {
    this.loading = true;
    this.userService.loadMostAreasAndSpecializations().subscribe({
      next: (response:any) => {
        console.log(response);
        this.registeredStats = response.data;
        this.cd.detectChanges();
        this.loading = false;
      },
      error: (err:any) => {
        console.error('Failed to load client statistics', err);
        this.loading = false;
      },
    });
  }

  loadMostSoldUnits() {
    this.loading = true;
    this.userService.loadMostSoldUnits().subscribe({
      next: (response:any) => {
        console.log(response);
        this.mostSold = response.data.mostSoldUnits;
        this.avgUnitPrice = response.data.mostUnitsWithAverage;
        this.monthlyAdvertisement = response.data.monthlyAdvertisementCount;
        this.cd.detectChanges();
        this.loading = false;
      },
      error: (err:any) => {
        console.error('Failed to load client statistics', err);
        this.loading = false;
      },
    });
  }

  loadMostSubscriptionAccounts() {
    this.loading = true;
    this.userService.loadMostSubscriptionAccounts().subscribe({
      next: (response:any) => {
        console.log(response);
        this.subscriptionsAccounts = response.data;
        this.cd.detectChanges();
        this.loading = false;
      },
      error: (err:any) => {
        console.error('Failed to load client statistics', err);
        this.loading = false;
      },
    });
  }

  formatPrice(price: number): string {
    if (price >= 1000000) {
      return (price / 1000000).toFixed(1) + ' مليون جنيه';
    } else if (price >= 1000) {
      return (price / 1000).toFixed(0) + ' ألف جنيه';
    }
    return price.toLocaleString() + ' جنيه';
  }

  formatNumber(num: number): string {
    return num.toLocaleString();
  }

  reload(){
    document.location.reload();
  }

  refresh() {
    this.loadMostRequestedUnits();
    this.loadMostSoldUnits();
    this.loadMostAreasAndSpecializations();
    this.loadMostSubscriptionAccounts();
    this.loadNoOfUsers();
  }
}
