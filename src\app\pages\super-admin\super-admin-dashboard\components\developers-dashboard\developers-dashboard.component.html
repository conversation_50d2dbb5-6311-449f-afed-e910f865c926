<div class="container-fluid px-4 py-5" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
  <!-- Header -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card border-0 shadow-sm">
        <div class="card-body p-4">
          <div class="d-flex align-items-center justify-content-between w-100">

            <!-- للعربية: الزر أولاً (أقصى الشمال) -->
            <div *ngIf="translationService.isRTL()" class="order-1">
              <button class="btn btn-primary btn-sm" (click)="refresh()"
                [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                <i class="ki-duotone ki-arrows-circle fs-4 ms-2">
                  <span class="path1"></span>
                  <span class="path2"></span>
                </i>
                {{ 'SUPER_ADMIN.DASHBOARD.DEVELOPERS.REFRESH_DATA' | translate }}
              </button>
            </div>

            <!-- المحتوى -->
            <div [class.text-end]="translationService.isRTL()" [class.text-start]="!translationService.isRTL()"
              [class.order-2]="translationService.isRTL()">
              <div class="d-flex align-items-center mb-2" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'"
                [style.gap]="translationService.isRTL() ? '1rem' : '0.75rem'">
                <div class="symbol symbol-40px" [class.me-4]="!translationService.isRTL()"
                  [class.ms-4]="translationService.isRTL()" [style.flex-shrink]="'0'">
                  <div class="symbol-label bg-light-primary">
                    <i class="ki-duotone ki-office-bag fs-3 text-primary">
                      <span class="path1"></span>
                      <span class="path2"></span>
                      <span class="path3"></span>
                      <span class="path4"></span>
                    </i>
                  </div>
                </div>
                <div [style.flex]="'1'" [style.min-width]="'0'">
                  <h1 class="text-gray-900 fw-bold mb-0 fs-2"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.margin-right]="translationService.isRTL() ? '0.5rem' : '0'"
                    [style.margin-left]="!translationService.isRTL() ? '0.5rem' : '0'"
                    [style.margin-bottom]="'0.25rem'">
                    {{ 'SUPER_ADMIN.DASHBOARD.DEVELOPERS.TITLE' | translate }}
                  </h1>
                  <p class="text-muted mb-0 fs-6"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.margin-right]="translationService.isRTL() ? '0.5rem' : '0'"
                    [style.margin-left]="!translationService.isRTL() ? '0.5rem' : '0'">
                    {{ 'SUPER_ADMIN.DASHBOARD.DEVELOPERS.DESCRIPTION' | translate }}
                  </p>
                </div>
              </div>
            </div>

            <!-- للإنجليزية: الزر آخراً (اليمين) -->
            <div *ngIf="!translationService.isRTL()">
              <button class="btn btn-primary btn-sm" (click)="refresh()">
                <i class="ki-duotone ki-arrows-circle fs-4 me-2">
                  <span class="path1"></span>
                  <span class="path2"></span>
                </i>
                {{ 'SUPER_ADMIN.DASHBOARD.DEVELOPERS.REFRESH_DATA' | translate }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Statistics Cards -->
  <div class="row g-6 mb-8">
    <div class="col-lg-3 col-md-6">
      <div class="card card-flush h-100 bg-primary">
        <div class="card-body text-white">
          <div class="d-flex align-items-center w-100" [class.flex-row-reverse]="translationService.isRTL()"
            [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <div class="symbol" [class.symbol-50px]="!translationService.isRTL()"
              [class.symbol-40px]="translationService.isRTL()" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <div class="symbol-label bg-white bg-opacity-20">
                <i class="fas fa-users text-white" [class.fs-2]="!translationService.isRTL()"
                  [class.fs-3]="translationService.isRTL()"></i>
              </div>
            </div>
            <div [class.text-end]="translationService.isRTL()" [class.text-start]="!translationService.isRTL()">
              <div class="fs-2 fw-bold">{{ formatNumber(totalDevelopers) | arabicNumbers }}</div>
              <div class="fs-7 text-white-75"
                [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                {{ 'SUPER_ADMIN.DASHBOARD.DEVELOPERS.TOTAL_DEVELOPERS' | translate }} - {{ totalComplaints |
                arabicNumbers }} {{ 'SUPER_ADMIN.DASHBOARD.DEVELOPERS.COMPLAINTS' | translate }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-3 col-md-6">
      <div class="card card-flush h-100 bg-dark-blue">
        <div class="card-body text-white">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-50px me-3">
              <div class="symbol-label bg-white bg-opacity-20">
                <i class="fas fa-user-check fs-2 text-white"></i>
              </div>
            </div>
            <div>
              <div class="fs-2 fw-bold">{{ formatNumber(totalProjects) }}</div>
              <div class="fs-7 text-white-75">Total Projects</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-3 col-md-6">
      <div class="card card-flush h-100 bg-mid-blue">
        <div class="card-body text-white">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-50px me-3">
              <div class="symbol-label bg-white bg-opacity-20">
                <i class="fas fa-building fs-2 text-white"></i>
              </div>
            </div>
            <div>
              <div class="fs-2 fw-bold">{{ formatNumber(totalUnits) }}</div>
              <div class="fs-7 text-white-75">Total Units </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-3 col-md-6">
      <div class="card card-flush h-100 bg-success">
        <div class="card-body text-white">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-50px me-3">
              <div class="symbol-label bg-white bg-opacity-20">
                <i class="fas fa-calendar-alt fs-2 text-white"></i>
              </div>
            </div>
            <div>
              <div class="fs-2 fw-bold">{{ totalSoldUnits }}</div>
              <div class="fs-7 text-white-75"> Total Sold Units</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Projects & Models -->
  <div class="row g-6 mb-8">

    <div class="col-lg-6">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-clock me-2"></i>
            Top Sale Projects
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="table-responsive">
            <table class="table table-row-bordered align-middle gs-0 gy-3">
              <thead>
                <tr class="fw-bold text-muted bg-light">
                  <th class="ps-4 rounded-start"> Project Name</th>
                  <th class="ps-4 rounded-start"> Project Designer</th>
                  <th>Sold Units Count</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let project of topSoldProjects">
                  <td class="ps-4">
                    <span class="text-dark fw-bold">{{ project.projectName }}</span>
                  </td>
                  <td class="ps-4">
                    <span class="text-dark fw-bold">{{ project.projectDesigner }}</span>
                  </td>
                  <td>
                    <span class="badge badge-light-primary">{{ project.unitSoldCount }}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Project Status Statistics -->
    <div class="col-lg-6">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-clock me-2"></i>
            Top Sale Models
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="table-responsive">
            <table class="table table-row-bordered align-middle gs-0 gy-3">
              <thead>
                <tr class="fw-bold text-muted bg-light">
                  <th class="ps-4 rounded-start"> Model Code </th>
                  <th class="ps-4 rounded-start"> Project Name</th>
                  <th> Sold Units Count </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let model of topSoldModels">
                  <td class="ps-4">
                    <span class="text-dark fw-bold">{{ model.modelCode }}</span>
                  </td>
                  <td class="ps-4">
                    <span class="text-dark fw-bold">{{ model.projectName }}</span>
                  </td>
                  <td>
                    <span class="badge badge-light-primary">{{ model.unitsSoldCount }}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Top Developers and Revenue Stats -->
    <div class="row g-6 mb-8">
      <!-- Top Developers -->
      <div class="col-lg-8">
        <div class="card card-flush h-100">
          <div class="card-header border-0 pt-6">
            <h3 class="card-title text-dark-blue fw-bold">
              <i class="fas fa-trophy me-2"></i>
              Avg Unit Price per Meter
            </h3>
          </div>
          <div class="card-body pt-0">
            <div class="table-responsive">
              <table class="table table-row-bordered align-middle gs-0 gy-3">
                <thead>
                  <tr class="fw-bold text-muted bg-light">
                    <th class="ps-4 rounded-start">Area</th>
                    <th>Unit type</th>
                    <th>Avg Price per Meter</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let unit of unitPriceStatistics; let i = index">
                    <td class="ps-4">
                      <div class="d-flex align-items-center">
                        <div class="symbol symbol-40px me-3">
                          <div class="symbol-label bg-light-primary">
                            <span class="text-primary fw-bold">{{ i + 1 }}</span>
                          </div>
                        </div>
                        <div>
                          <span class="text-dark fw-bold">{{ unit.area }}</span>
                        </div>
                      </div>
                    </td>
                    <td>
                      <span class="text-muted">{{ unit.unitType }}</span>
                    </td>
                    <td>
                      <span class="badge badge-light-primary">{{ formatNumber(unit.averagePricePermeterInCash) }}</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Revenue Statistics -->
      <!-- <div class="col-lg-4">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-chart-line me-2"></i>
            إحصائيات الإيرادات
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="row g-4">
            <div class="col-12">
              <div class="bg-light-success p-4 rounded">
                <div class="text-success fs-6 fw-bold">إجمالي الإيرادات</div>
                <div class="text-dark fs-3 fw-bolder">{{ formatPrice(revenueStats.totalRevenue) }}</div>
              </div>
            </div>
            <div class="col-12">
              <div class="bg-light-primary p-4 rounded">
                <div class="text-primary fs-6 fw-bold">الإيرادات الشهرية</div>
                <div class="text-dark fs-3 fw-bolder">{{ formatPrice(revenueStats.monthlyRevenue) }}</div>
              </div>
            </div>
            <div class="col-12">
              <div class="bg-light-warning p-4 rounded">
                <div class="text-warning fs-6 fw-bold">متوسط قيمة المشروع</div>
                <div class="text-dark fs-3 fw-bolder">{{ formatPrice(revenueStats.averageProjectValue) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div> -->
    </div>

  </div>