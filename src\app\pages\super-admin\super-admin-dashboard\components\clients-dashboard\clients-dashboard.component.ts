import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { UsersService } from '../../../services/users.service';
import { TranslationService } from '../../../../../modules/i18n/translation.service';

@Component({
  selector: 'app-clients-dashboard',
  templateUrl: './clients-dashboard.component.html',
  styleUrls: ['./clients-dashboard.component.scss']
})
export class ClientsDashboardComponent implements OnInit {

  // Client statistics
  loading = false;
  totalClients: number = 0;
  totalComplaints: number = 0;
  RegisteredMonthlyActivity: any[] = [];
  visitedMonthlyActivity: any[] = [];
  requestTypeStats: any[] = [];
  mostRequests: any[] = [];
  totalRequests: number = 0;
  totalVisited: number = 0;

  constructor(
    private cd: ChangeDetectorRef,
    private userService: UsersService,
    public translationService: TranslationService
  ) {}

  ngOnInit() {
    this.loadClientStatistics();
    this.loadNoOfClients();
    this.loadNoOfClientsComplaints();
    this.loadRequestTypeStats();
  }

  refresh() {
    try {
      this.loadClientStatistics();
      this.loadNoOfClients();
      this.loadNoOfClientsComplaints();
      this.loadRequestTypeStats();
    } catch (error) {
      console.error('Error refreshing data:', error);
    }
  }

  loadClientStatistics() {
    this.loading = true;
    this.userService.loadRegisteredMonthlyActivity().subscribe({
      next: (response:any) => {
        console.log(response);
        this.RegisteredMonthlyActivity = response.data;
        this.cd.detectChanges();
      },
      error: (err:any) => {
        console.error('Failed to load client statistics', err);
      },
    });

    this.userService.loadVisitedMonthlyActivity().subscribe({
      next: (response:any) => {
        console.log(response);
        this.visitedMonthlyActivity = response.data.count.monthlyVisitors;
        this.totalVisited = response.data.count.totalCountOfVisitors;
        this.cd.detectChanges();
      },
      error: (err:any) => {
        console.error('Failed to load client statistics', err);
      },
    });
    this.loading = false;
  }

 loadNoOfClients() {
    this.loading = true;
    this.userService.loadNoOfClients().subscribe({
      next: (response:any) => {
        const roles = response.data;
        const clientData = roles.find((item: any) => item.role === 'client');
        this.totalClients = clientData ? clientData.count : 0;
        this.cd.detectChanges();
        this.loading = false;
      },
      error: (err:any) => {
        console.error('Failed to load client statistics', err);
        this.loading = false;
      },
    });
  }

  loadNoOfClientsComplaints() {
    this.loading = true;
    this.userService.loadNoOfClientsComplaints().subscribe({
      next: (response:any) => {
        console.log(response);
        this.totalComplaints = response.data.clientComplaints;
        this.cd.detectChanges();
        this.loading = false;
      },
      error: (err:any) => {
        console.error('Failed to load client statistics', err);
        this.loading = false;
      },
    });
  }

  loadRequestTypeStats() {
    this.loading = true;
    this.userService.loadRequestTypeStats().subscribe({
      next: (response:any) => {
        console.log(response);
        const stats = response.data.unitOperationCount;
        const total = stats.reduce((sum: number, item: any) => sum + item.count, 0);
        this.totalRequests = total;
        this.requestTypeStats = stats.map((item: any) => ({
          ...item,
          percentage: total > 0 ? ((item.count / total) * 100).toFixed(0) : '0'
        }));

        const stats2 = response.data.mostUnitTypeAreaCount;
        const total2 = stats2.reduce((sum: number, item: any) => sum + item.count, 0);
        this.mostRequests = stats2.map((item: any) => ({
          ...item,
          percentage: total2 > 0 ? ((item.count / total2) * 100).toFixed(0) : '0'
        }));

        this.cd.detectChanges();
        this.loading = false;
      },
      error: (err:any) => {
        console.error('Failed to load client statistics', err);
        this.loading = false;
      },
    });
  }


  // loadTopClients() {}
  // loadRequestStats() {}
  // loadClientBehavior() {}
  // loadSatisfactionStats() {}
  // loadBudgetRangeStats() {}
  // loadGeographicStats() {}

  formatPrice(price: number): string {
    if (price >= 1000000) {
      return (price / 1000000).toFixed(1) + ' مليون جنيه';
    } else if (price >= 1000) {
      return (price / 1000).toFixed(0) + ' ألف جنيه';
    }
    return price.toLocaleString() + ' جنيه';
  }

  formatNumber(num: number): string {
    return num.toLocaleString();
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'جديد': return 'primary';
      case 'قيد المراجعة': return 'warning';
      case 'مكتمل': return 'success';
      case 'ملغي': return 'danger';
      case 'نشط': return 'success';
      default: return 'secondary';
    }
  }

  getPerformanceColor(value: number): string {
    if (value >= 80) return 'success';
    if (value >= 60) return 'primary';
    if (value >= 40) return 'warning';
    return 'danger';
  }

  getSatisfactionColor(type: string): string {
    switch (type) {
      case 'veryHappy': return 'success';
      case 'happy': return 'primary';
      case 'neutral': return 'warning';
      case 'unhappy': return 'danger';
      case 'veryUnhappy': return 'dark';
      default: return 'secondary';
    }
  }

  getSatisfactionLabel(type: string): string {
    switch (type) {
      case 'veryHappy': return 'راضي جداً';
      case 'happy': return 'راضي';
      case 'neutral': return 'محايد';
      case 'unhappy': return 'غير راضي';
      case 'veryUnhappy': return 'غير راضي جداً';
      default: return '';
    }
  }

  getConversionColor(rate: number): string {
    if (rate >= 8) return 'success';
    if (rate >= 6) return 'primary';
    if (rate >= 4) return 'warning';
    return 'danger';
  }
}
