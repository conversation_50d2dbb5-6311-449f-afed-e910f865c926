import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { BrokerService } from '../services/broker.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { TranslationService } from '../../../modules/i18n';
import { BaseGridComponent } from '../../shared/base-grid/base-grid.component';

@Component({
  selector: 'app-broker-dashboard',
  templateUrl: './broker-dashboard.component.html',
  styleUrl: './broker-dashboard.component.scss',
})
export class BrokerDashboardComponent implements OnInit {

  @Input() cssClass: string = ''

  userId: any;
  brokerId :any;
  currentLanguage: string = 'en';
  allRequestsCount: number = 0;
  newRequestsCount: number = 0;
  inProcessingRequestsCount: number = 0;
  finishedRequestsCount: number = 0;
  newRequestsPercent: number = 0;
  inProcessingRequestsPercent: number = 0;
  finishedRequestsPercent: number = 0;

  totalContractRequests: number = 0;
  acceptedContractsCount: number = 0;
  maps: number = 0;
  dataAndProperties: number = 0;
  advertisements: number = 0;
  loading = false;
  errorMessage = '';

  constructor(
    protected cd: ChangeDetectorRef,
    private brokerService: BrokerService,
    private translateService: TranslateService,
    private translationService: TranslationService
  ) {
    // Initialize language
    this.currentLanguage = this.translationService.getCurrentLanguage();
    this.translateService.use(this.currentLanguage);
  }

  ngOnInit(): void {
    const userJson = localStorage.getItem('currentUser');
    let user = userJson ? JSON.parse(userJson) : null;
    this.userId = user?.id;
    this.brokerId = user?.brokerId;
    this.fetchStatistics();
    this.fetchDashboardStatistics();
  }

  propertySvg = `
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <mask id="mask0_448_13442" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="20">
        <path d="M19.5 19.5V0.5H0.5V19.5H19.5Z" fill="white" stroke="white"/>
      </mask>
      <g mask="url(#mask0_448_13442)">
        <path d="M11.3698 19.259H2.64697V4.72903C2.64697 4.32169 2.90709 3.95989 3.29318 3.83013L11.3698 1.11528V19.259Z" stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M16.2795 17.4364V19.259H11.3701V9.41489H16.7869C17.1459 9.41489 17.4365 9.70552 17.4365 10.0645V15.2754" stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M11.3701 19.259H19.2667C19.51 19.259 19.7072 19.0618 19.7072 18.8186V17.4365H11.3701V19.259Z" stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M17.9105 15.2046H18.0759C18.9767 15.2046 19.707 15.9349 19.707 16.8357V17.436H16.2793V16.8357C16.2793 15.9349 17.0096 15.2046 17.9105 15.2046Z" stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M0.733438 19.259H2.64711V17.4365H0.292969V18.8186C0.292969 19.0618 0.490156 19.259 0.733438 19.259Z" stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M2.4168 15.2046H2.64715V17.436H0.292969V17.3284C0.292969 16.1554 1.24387 15.2046 2.4168 15.2046Z" stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M14.4034 9.41431H11.3701V1.11485L14.1142 3.76731C14.299 3.94599 14.4034 4.19208 14.4034 4.44919V8.03329" stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M4.78076 3.32971V1.3512C4.78076 1.01487 5.05342 0.742212 5.39014 0.742212H5.45186C5.78818 0.742212 6.06084 1.01487 6.06084 1.3512V2.89924" stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M11.3698 3.32935L4.78076 5.54419" stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M11.3698 5.43787L4.78076 7.65271" stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M11.3698 7.54651L4.78076 9.76135" stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M11.3698 9.65552L4.78076 11.8704" stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M5.8685 13.6125L4.78076 13.9781" stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M11.37 11.7633L7.18066 13.1715" stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M8.25287 19.259H4.78076V16.7702C4.78076 16.5772 4.90928 16.4079 5.09514 16.356L7.82197 15.5947C8.03846 15.5343 8.25287 15.697 8.25287 15.9218V19.259Z" stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M15.8193 11.8313H12.9462C12.8352 11.8313 12.7451 11.7413 12.7451 11.6302V10.925C12.7451 10.8139 12.8352 10.7239 12.9462 10.7239H15.8193C15.9304 10.7239 16.0204 10.8139 16.0204 10.925V11.6302C16.0204 11.7413 15.9304 11.8313 15.8193 11.8313Z" stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M15.8193 14.0582H12.9462C12.8352 14.0582 12.7451 13.9682 12.7451 13.8571V13.1519C12.7451 13.0408 12.8352 12.9508 12.9462 12.9508H15.8193C15.9304 12.9508 16.0204 13.0408 16.0204 13.1519V13.8571C16.0204 13.9682 15.9304 14.0582 15.8193 14.0582Z" stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M13.8965 16.3091H12.9462C12.8352 16.3091 12.7451 16.219 12.7451 16.108V15.4028C12.7451 15.2917 12.8352 15.2017 12.9462 15.2017H13.8965C14.0076 15.2017 14.0977 15.2917 14.0977 15.4028V16.108C14.0977 16.219 14.0076 16.3091 13.8965 16.3091Z" stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M6.51709 15.9591V19.259" stroke="#0D47A1" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
      </g>
    </svg>
  `;

  adSvg = `
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="12" cy="12" r="10" fill="#1a3c8b"/>
    </svg>
  `;

  developerSvg = `
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clip-path="url(#clip0_136_62239)">
        <path stroke="#0D47A1" fill="#0D47A1" stroke-width="0.5" d="M19.4998 18.9999H18.9998V11H17.9998V10H16.9998V11H15.9998V13H14.9999V11V9.00003H13.9999V11H12.9999V8.00004H11.9999V11V13H10.9999V11H9.9999V9.00003H8.99991V11H7.99992V18.9999H5.99994V8.00004H6.99993C7.55227 8.00004 7.99992 7.55239 7.99992 7.00005V6.00006H16.9998V9.00003H17.9998V6.00006H18.9998C19.4635 6.00006 19.8665 5.68108 19.9731 5.22972C20.0798 4.7784 19.8618 4.31305 19.4471 4.10575L17.4471 3.10576C17.3082 3.03642 17.1551 3.00009 16.9998 3.00009H6.10928L4.89429 0.552773C4.7223 0.208792 4.3723 0.00012207 4.00031 0.00012207C3.92399 0.00012207 3.84699 0.00879386 3.77031 0.027114C3.31895 0.133441 2.99997 0.536445 2.99997 1.00011V3.00009H0.99999C0.447652 3.00009 0 3.44774 0 4.00008V5.00007C0 5.55241 0.447652 6.00006 0.99999 6.00006H2.99997V7.00005V18.9999H0.499995C0.22367 18.9999 0 19.2236 0 19.4999C0 19.7763 0.22367 19.9999 0.499995 19.9999H19.4998C19.7761 19.9999 19.9998 19.7763 19.9998 19.4999C19.9998 19.2236 19.7761 18.9999 19.4998 18.9999ZM2.99997 5.00007H0.99999V4.00008H2.99997V5.00007ZM16.9998 4.00008L18.9998 5.00007H7.99992C7.99992 4.73472 7.89457 4.48043 7.70692 4.29309L7.41426 4.00008H16.9998ZM3.99996 1.00011L4.99995 3.00009H3.99996V1.00011ZM3.99996 4.00008H5.99994L6.99993 5.00007V7.00005H3.99996V4.00008ZM4.99995 18.9999H3.99996V8.00004H4.99995V18.9999ZM17.9998 18.9999H8.99991V12H9.9999V14H12.9999V12H13.9999V14H16.9998V12H17.9998V18.9999Z"/>
      </g>
      <defs>
        <clipPath id="clip0_136_62239">
          <rect width="20" height="20" fill="white"/>
        </clipPath>
      </defs>
    </svg>
  `;

  onFilterChanged(payload: { specializationScope: string | null }): void {
    this.fetchStatistics(payload.specializationScope);
  }

  fetchStatistics(specializationScope?: string | null): void {
    this.loading = true;

    this.brokerService.getBrokerRequestStatistics(this.userId, specializationScope).subscribe({
      next: (response: any) => {
        this.allRequestsCount = response.data.allRequestsCount || 0;
        this.newRequestsCount = response.data.newRequestsCount || 0;
        this.inProcessingRequestsCount = response.data.inProcessingRequestsCount || 0;
        this.finishedRequestsCount = response.data.finishedRequestsCount || 0;

        const total = this.allRequestsCount || 1;
        this.newRequestsPercent = parseFloat(((this.newRequestsCount / total) * 100).toFixed(1));
        this.inProcessingRequestsPercent = parseFloat(((this.inProcessingRequestsCount / total) * 100).toFixed(1));
        this.finishedRequestsPercent = parseFloat(((this.finishedRequestsCount / total) * 100).toFixed(1));

        this.cd.detectChanges();
        this.loading = false;
      },
      error: (error) => {
        this.errorMessage = 'Failed to load requests';
        console.error(error);
        this.loading = false;
      }
    });
  }

  fetchDashboardStatistics(): void {

    this.loading = true;
    this.brokerService.getBrokerDashboardStatistics(this.brokerId).subscribe({
      next: (response: any) => {
        this.totalContractRequests = response.data.totalContractRequests || 0;
        this.acceptedContractsCount = response.data.acceptedContractsCount || 0;
        this.maps = response.data.maps || 0;
        this.dataAndProperties = response.data.dataAndProperties || 0;
        this.advertisements = response.data.advertisements || 0;

        this.cd.detectChanges();
        this.loading = false;
      },
      error: (error) => {
        this.errorMessage = 'Failed to load broker statistics';
        console.error(error);
        this.loading = false;
      }
    });
  }
}
