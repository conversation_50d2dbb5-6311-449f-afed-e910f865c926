import { Pipe, PipeTransform } from '@angular/core';
import { TranslationService } from '../../modules/i18n/translation.service';

@Pipe({
  name: 'arabicNumbers',
  pure: false,
  standalone: true
})
export class ArabicNumbersPipe implements PipeTransform {

  constructor(private translationService: TranslationService) {}

  transform(value: number | string): string {
    if (value === null || value === undefined) {
      return '0';
    }

    const stringValue = value.toString();

    // Check if current language is Arabic
    const currentLang = this.translationService.getSelectedLanguage();

    if (currentLang === 'ar') {
      // Convert English numbers to Arabic numbers
      const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
      return stringValue.replace(/[0-9]/g, (digit) => arabicNumbers[parseInt(digit)]);
    }

    return stringValue;
  }
}
