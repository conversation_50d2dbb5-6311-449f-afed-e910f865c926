// Main container responsive padding
.container-fluid {
  margin-left: auto;
  margin-right: auto;

  // Small screens (mobile)
  @media (max-width: 576px) {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  // Medium screens (tablet)
  @media (min-width: 577px) and (max-width: 992px) {
    padding-left: 2rem !important;
    padding-right: 2rem !important;
  }

  // Large screens (desktop)
  @media (min-width: 993px) {
    padding-left: 3rem !important;
    padding-right: 3rem !important;
    max-width: 1400px;
  }

  // Extra large screens
  @media (min-width: 1400px) {
    padding-left: 4rem !important;
    padding-right: 4rem !important;
  }
}

.cursor-pointer {
  cursor: pointer;
}

.card {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border: 1px solid rgba(0, 0, 0, 0.125);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  }
}

.badge {
  &.bg-light-primary {
    background-color: rgba(var(--bs-primary-rgb), 0.1) !important;
  }

  &.bg-light-success {
    background-color: rgba(var(--bs-success-rgb), 0.1) !important;
  }
}

.form-check-input:disabled {
  opacity: 0.7;
}

.form-check-label {
  font-size: 0.875rem;
}

// Image hover effects
.position-relative img {
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.02);
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .col-lg-8,
  .col-lg-4 {
    margin-bottom: 1rem;
  }

  .card-body {
    padding: 1rem;
  }

  // Adjust row margins on mobile
  .row {
    margin-left: -0.5rem;
    margin-right: -0.5rem;

    > [class*="col-"] {
      padding-left: 0.5rem;
      padding-right: 0.5rem;
    }
  }
}

// Loading spinner
.spinner-border {
  width: 3rem;
  height: 3rem;
}

// Custom button styles
.btn {
  border-radius: 0.5rem;
  font-weight: 500;

  &.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;

    &:hover {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
      transform: translateY(-1px);
    }
  }
}

// Price styling
.text-success {
  color: #28a745 !important;
  font-size: 1.5rem;
}

// Icon styling
.fa-solid {
  &.text-primary {
    color: #667eea !important;
  }
}

// Unit image container styling
.unit-image-container {
  height: 400px;
  border-radius: 0.5rem;
  overflow: hidden;
}

.unit-image-background {
  height: 100%;
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(0, 0, 0, 0.8) 100%
  );
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.upload-section {
  color: #6c757d;

  .upload-icon {
    margin-bottom: 1rem;
  }

  h5 {
    color: #0d6efd !important;
    font-weight: 600;
  }

  p {
    font-size: 0.875rem;
    color: #6c757d !important;
  }
}

.unit-info-overlay {
  background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.8) 100%);

  h2 {
    font-size: 1.75rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  }

  p {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }
}

// Pink badge styling
.bg-pink {
  background-color: #e91e63 !important;
}

// Property details text styling
.property-details-text {
  p {
    line-height: 1.6;
    color: #495057;
    font-size: 0.95rem;
  }
}

// RTL Support for Unit Details
.rtl-layout {
  direction: rtl;
  text-align: right;

  .card-title {
    font-family: 'Hacen Liner Screen St', sans-serif;
    font-size: 1.3rem;
    font-weight: bold;
  }

  .property-details-text {
    font-family: 'Hacen Liner Screen St', sans-serif;
    text-align: right;

    p {
      font-size: 1.1rem;
      line-height: 1.9;
    }
  }

  .badge {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .text-muted {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .fw-semibold {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .form-check-label {
    font-family: 'Hacen Liner Screen St', sans-serif;
    font-size: 1rem;
  }

  .unit-info-overlay {
    h2 {
      font-family: 'Hacen Liner Screen St', sans-serif;
    }

    p {
      font-family: 'Hacen Liner Screen St', sans-serif;
    }
  }

  // Reverse margins and paddings
  .me-2 {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
  }

  .me-3 {
    margin-left: 1rem !important;
    margin-right: 0 !important;
  }

  // Only reverse directional icons that make sense
  .fa-location-dot {
    transform: scaleX(-1);
  }

  // Don't reverse these icons as they look better normal
  .fa-file-image,
  .fa-bed,
  .fa-bath,
  .fa-ruler-combined,
  .fa-building,
  .fa-check-circle {
    transform: none !important;
  }

  // Reverse carousel indicators and controls
  .carousel-indicators {
    direction: ltr;
  }

  // Specific icon fixes
  .unit-info-overlay {
    .fa-location-dot {
      transform: scaleX(-1);
    }

    .fa-check-circle {
      transform: none !important;
    }
  }

  // Card icons should not be transformed
  .card-body {
    .fa-bed,
    .fa-bath,
    .fa-ruler-combined,
    .fa-building {
      transform: none !important;
    }
  }

  // Reverse positioning
  .position-absolute.top-0.start-0 {
    right: 0 !important;
    left: auto !important;
  }

  .d-flex.align-items-center {
    flex-direction: row-reverse;

    // Fix margins for RTL
    i.fa-solid {
      margin-left: 0.5rem !important;
      margin-right: 0 !important;
      // Don't transform icons by default
      transform: none !important;
    }

    // Only transform specific directional icons
    i.fa-location-dot {
      transform: scaleX(-1) !important;
    }
  }

  // Responsive adjustments for Arabic
  @media (max-width: 768px) {
    .property-details-text p {
      font-size: 1rem;
    }
  }
}
