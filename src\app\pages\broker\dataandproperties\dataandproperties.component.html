<div class="mb-5 mt-0">
  <app-broker-title></app-broker-title>
</div>

<div class="card mb-5 mb-xl-10">
  <div class="card-body pt-3 pb-0">
    <!-- Header Section -->
    <div class="row mb-6">
      <div class="col-12">
        <div
          class="d-flex flex-column flex-lg-row align-items-start align-items-lg-center justify-content-between gap-3 mt-2"
          [class.flex-row-reverse]="translationService.isRTL()">
          <!-- Title Section - Right in Arabic, Left in English -->
          <div class="flex-shrink-0" [class.text-end]="translationService.isRTL()"
            [class.order-lg-3]="translationService.isRTL()" [class.order-lg-1]="!translationService.isRTL()">
            <h1 class="text-dark-blue fs-2 fs-lg-1 fw-bolder mb-1">
              {{ 'DATA_PROPERTIES.TITLE' | translate }}
            </h1>
            <p class="text-muted fs-6 mb-0">
              {{ 'DATA_PROPERTIES.SUBTITLE' | translate }}
            </p>
          </div>

          <!-- Center: Search -->
          <div class="flex-grow-1 mx-lg-4 order-lg-2" style="max-width: 400px">
            <div class="position-relative">
              <app-keenicon name="magnifier" [class]="translationService.isRTL() ?
                  'fs-3 text-gray-500 position-absolute top-50 translate-middle-y me-4' :
                  'fs-3 text-gray-500 position-absolute top-50 translate-middle-y ms-4'" type="outline">
              </app-keenicon>
              <input type="text" name="searchText" [class]="translationService.isRTL() ?
                  'form-control form-control-lg pe-12 bg-light border-0 rounded-3' :
                  'form-control form-control-lg ps-12 bg-light border-0 rounded-3'" [(ngModel)]="searchText"
                (ngModelChange)="onSearchTextChange($event)"
                [placeholder]="'DATA_PROPERTIES.SEARCH_PLACEHOLDER' | translate" />
            </div>
          </div>

          <!-- Action Buttons - Left in Arabic, Right in English -->
          <div class="flex-shrink-0" [class.order-lg-1]="translationService.isRTL()"
            [class.order-lg-3]="!translationService.isRTL()">
            <div class="d-flex flex-column flex-sm-row gap-2" [class.flex-row-reverse]="translationService.isRTL()">
              <!-- Filter Button -->
              <div class="position-relative">
                <button type="button" class="btn btn-light-primary btn-sm px-3 py-2" (click)="toggleFilterDropdown()">
                  <i [class]="translationService.isRTL() ? 'fa-solid fa-filter ms-2' : 'fa-solid fa-filter me-2'"></i>
                  <span class="d-none d-md-inline">{{ 'DATA_PROPERTIES.FILTER' | translate }}</span>
                </button>

                <!-- Filter Dropdown -->
                <div *ngIf="isFilterDropdownVisible" class="dropdown-menu show p-3 shadow-lg border-0 rounded-3" style="
                    position: absolute;
                    top: 100%;
                    right: 0;
                    z-index: 1000;
                    min-width: 280px;
                    max-width: 90vw;
                  ">
                  <app-unit-filter [brokerId]="brokerId" (filtersApplied)="onFiltersApplied($event)"></app-unit-filter>
                </div>
              </div>

              <!-- Upload Units -->
              <input type="file" #fileInput (change)="onFileSelected($event)" accept=".xlsx,.xls" hidden />
              <button type="button" class="btn btn-light-success btn-sm px-3 py-2" (click)="fileInput.click()">
                <i [class]="translationService.isRTL() ? 'fa-solid fa-upload ms-2' : 'fa-solid fa-upload me-2'"></i>
                <span class="d-none d-md-inline">{{ 'DATA_PROPERTIES.UPLOAD' | translate }}</span>
              </button>

              <!-- Download Template -->
              <button type="button" class="btn btn-light-info btn-sm px-3 py-2" (click)="downloadTemplate()">
                <i [class]="translationService.isRTL() ? 'fa-solid fa-download ms-2' : 'fa-solid fa-download me-2'"></i>
                <span class="d-none d-md-inline">{{ 'DATA_PROPERTIES.TEMPLATE' | translate }}</span>
              </button>

              <!-- Add New Property -->
              <button type="button" class="btn btn-primary btn-sm px-3 py-2 fw-bold"
                [routerLink]="['/broker/add-property']">
                <i [class]="translationService.isRTL() ? 'fa-solid fa-plus ms-2' : 'fa-solid fa-plus me-2'"></i>
                <span class="d-none d-lg-inline">{{ 'DATA_PROPERTIES.ADD_UNIT' | translate }}</span>
                <span class="d-lg-none">{{ 'DATA_PROPERTIES.ADD' | translate }}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div *ngIf="isLoading" class="d-flex justify-content-center align-items-center py-10">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">{{ 'DATA_PROPERTIES.LOADING' | translate }}</span>
      </div>
    </div>

    <!-- Simple Filter Section -->
    <div *ngIf="
        !isLoading && !showEmptyCard && !showSuccessCard && !showPublishCard
      " class="mb-3">
      <div class="card border-0 shadow-sm">
        <div class="card-body p-3">
          <div class="row g-3 align-items-end">
            <div class="col-md-4">
              <label class="form-label fw-semibold">{{ 'DATA_PROPERTIES.COMPOUND_TYPE' | translate }}</label>
              <select class="form-select form-select-sm" [(ngModel)]="currentCompoundType"
                (ngModelChange)="onCompoundTypeChange()">
                <option value="" disabled>{{ 'DATA_PROPERTIES.ALL_COMPOUND_TYPES' | translate }}</option>
                <option value="outside_compound">{{ 'DATA_PROPERTIES.OUTSIDE_COMPOUND' | translate }}</option>
                <option value="inside_compound">{{ 'DATA_PROPERTIES.INSIDE_COMPOUND' | translate }}</option>
                <option value="village">{{ 'DATA_PROPERTIES.VILLAGE' | translate }}</option>
              </select>
            </div>

            <div class="col-md-4">
              <label class="form-label fw-semibold">{{ 'DATA_PROPERTIES.UNIT_TYPE' | translate }}</label>
              <select class="form-select form-select-sm" [(ngModel)]="currentUnitType"
                [disabled]="!currentCompoundType">
                <option value="" disabled>
                  {{
                  currentCompoundType
                  ? ('DATA_PROPERTIES.SELECT_UNIT_TYPE' | translate)
                  : ('DATA_PROPERTIES.SELECT_COMPOUND_TYPE_FIRST' | translate)
                  }}
                </option>
                <option *ngFor="let unitType of getAvailableUnitTypes()" [value]="unitType.value">
                  {{ unitType.key }}
                </option>
              </select>
            </div>

            <div class="col-md-4">
              <div class="d-flex h-100 align-items-end gap-2" [class.flex-row-reverse]="translationService.isRTL()">
                <button class="btn btn-primary w-10" (click)="applyFilters()" [disabled]="!canApplyFilters()"
                  style="height: 40px">
                  <i [class]="translationService.isRTL() ? 'fa-solid fa-filter ms-2' : 'fa-solid fa-filter me-2'"></i>
                  {{ 'DATA_PROPERTIES.APPLY_FILTER' | translate }}
                </button>
                <button class="btn btn-outline-danger" (click)="clearAllFilters()" style="height: 40px">
                  {{ 'DATA_PROPERTIES.CLEAR_ALL_FILTERS' | translate }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Properties Table -->
    <app-propertiestable *ngIf="
        !isLoading && !showEmptyCard && !showSuccessCard && !showPublishCard
      " [appliedFilters]="appliedFilters">
    </app-propertiestable>

    <!-- Empty Properties Card -->
    <app-empty-properties-card *ngIf="!isLoading && showEmptyCard" [userRole]="user.role"
      [onFileUpload]="handleFileUpload.bind(this)" [onDownloadTemplate]="downloadTemplate.bind(this)">
    </app-empty-properties-card>

    <!-- Publish Property Card -->
    <app-publish-property-card *ngIf="!isLoading && showPublishCard" (backToTable)="onBackToTable()">
    </app-publish-property-card>

    <!-- Success Adding Property Card -->
    <app-success-adding-property-card *ngIf="!isLoading && showSuccessCard" (backToTable)="onBackToTable()">
    </app-success-adding-property-card>
  </div>
</div>
