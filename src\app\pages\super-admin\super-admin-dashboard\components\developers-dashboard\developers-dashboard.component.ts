import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { UsersService } from '../../../services/users.service';
import { TranslationService } from '../../../../../modules/i18n/translation.service';

@Component({
  selector: 'app-developers-dashboard',
  templateUrl: './developers-dashboard.component.html',
  styleUrls: ['./developers-dashboard.component.scss']
})
export class DevelopersDashboardComponent implements OnInit {

  loading = false;
  totalDevelopers: number = 0;
  totalComplaints: number = 0;
  totalUnits : number = 0;
  totalSoldUnits : number = 0;
  totalProjects: number = 0;
  topSoldModels: any[] = [];
  topSoldProjects: any[] = [];
  unitPriceStatistics: any[] = [];

  constructor(
    private cd: ChangeDetectorRef,
    private userService: UsersService,
    public translationService: TranslationService
  ) {}

  ngOnInit() {
    this.loadNoOfDevelopers();
    this.loadNoOfDevelopersComplaints();
    this.loadDeveloperStatistics();
  }

  refresh() {
    this.loadNoOfDevelopers();
    this.loadNoOfDevelopersComplaints();
    this.loadDeveloperStatistics();
  }

  loadNoOfDevelopers() {
    this.loading = true;
    this.userService.loadNoOfClients().subscribe({
      next: (response:any) => {
        const roles = response.data;
        const developerData = roles.find((item: any) => item.role === 'developer');
        this.totalDevelopers = developerData ? developerData.count : 0;
        this.cd.detectChanges();
        this.loading = false;
      },
      error: (err:any) => {
        console.error('Failed to load developer statistics', err);
        this.loading = false;
      },
    });
  }

   loadNoOfDevelopersComplaints() {
    this.loading = true;
    this.userService.loadNoOfClientsComplaints().subscribe({
      next: (response:any) => {
        console.log(response);
        this.totalComplaints = response.data.developerComplaints;
        this.cd.detectChanges();
        this.loading = false;
      },
      error: (err:any) => {
        console.error('Failed to load client statistics', err);
        this.loading = false;
      },
    });
  }

  loadDeveloperStatistics() {
    this.loading = true;
    this.userService.loadDeveloperStatistics().subscribe({
      next: (response:any) => {
        this.totalUnits = response.data.statistics.totalUnits;
        this.totalSoldUnits = response.data.statistics.totalSoldUnits;
        this.totalProjects = response.data.statistics.totalProjects;
        this.topSoldModels = response.data.topSoldModels;
        this.topSoldProjects = response.data.topSoldProjects;
        this.unitPriceStatistics = response.data.unitPriceStats
        this.cd.detectChanges();
        this.loading = false;
      },
      error: (err:any) => {
        console.error('Failed to load client statistics', err);
        this.loading = false;
      },
    });
  }

  formatPrice(price: number): string {
    if (price >= 1000000000) {
      return (price / 1000000000).toFixed(1) + ' مليار جنيه';
    } else if (price >= 1000000) {
      return (price / 1000000).toFixed(1) + ' مليون جنيه';
    } else if (price >= 1000) {
      return (price / 1000).toFixed(0) + ' ألف جنيه';
    }
    return price.toLocaleString() + ' جنيه';
  }

  formatNumber(num: number): string {
    return num.toLocaleString();
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'completed': return 'success';
      case 'underConstruction': return 'primary';
      case 'planning': return 'warning';
      case 'onHold': return 'danger';
      default: return 'secondary';
    }
  }

  getPerformanceColor(value: number): string {
    if (value >= 90) return 'success';
    if (value >= 80) return 'primary';
    if (value >= 70) return 'warning';
    return 'danger';
  }

  getRatingStars(rating: number): string[] {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push('fas fa-star text-warning');
    }

    if (hasHalfStar) {
      stars.push('fas fa-star-half-alt text-warning');
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push('far fa-star text-muted');
    }

    return stars;
  }
}
