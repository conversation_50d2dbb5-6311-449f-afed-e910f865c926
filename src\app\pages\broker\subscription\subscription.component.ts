import { SubscriptionService } from './../services/subscription.service';
import { ChangeDetectorRef, Component } from '@angular/core';
import { BaseGridComponent } from '../../shared/base-grid/base-grid.component';
import { TranslateService } from '@ngx-translate/core';
import { TranslationService } from '../../../modules/i18n';

@Component({
  selector: 'app-subscription',
  templateUrl: './subscription.component.html',
  styleUrl: './subscription.component.scss'
})

export class SubscriptionComponent extends BaseGridComponent{
  currentLanguage: string = 'en';

  constructor(
    protected cd : ChangeDetectorRef,
    private subscriptionService : SubscriptionService,
    private translateService: TranslateService,
    private translationService: TranslationService
  ) {
    super(cd);
    this.setService(subscriptionService);
    this.orderBy = 'id';
    this.orderDir = 'DESC';

    // Initialize language
    this.currentLanguage = this.translationService.getCurrentLanguage();
    this.translateService.use(this.currentLanguage);
  }

  // Get translated account name
  getTranslatedAccountName(name: string): string {
    const accountMap: { [key: string]: string } = {
      'Golden Account': 'BROKER.SUBSCRIPTION.GOLDEN_ACCOUNT',
      'Silver Account': 'BROKER.SUBSCRIPTION.SILVER_ACCOUNT',
      'Bronze Account': 'BROKER.SUBSCRIPTION.BRONZE_ACCOUNT'
    };

    const translationKey = accountMap[name];
    return translationKey ? this.translateService.instant(translationKey) : name;
  }
}
