import { Injectable } from '@angular/core';
import { <PERSON>ttp<PERSON>e<PERSON>, HttpHand<PERSON>, HttpEvent, HttpInterceptor } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {

  intercept(request: HttpRequest<any>, next: <PERSON>ttp<PERSON><PERSON><PERSON>): Observable<HttpEvent<any>> {
    // Get token from localStorage or a service
    const authToken = localStorage.getItem('authToken');
    console.log(localStorage.getItem('currentUser'));
    const publicEndpoints = ['/login', '/register', '/send-otp', '/check-otp', '/reset-password'];
    const isPublic = publicEndpoints.some(url => request.url.includes(url));


    const headersConfig: { [key: string]: string } = {
      Accept: 'application/json',
    };

    // Conditionally add Authorization
    if (authToken && !isPublic) {
      headersConfig['Authorization'] = `Bearer ${authToken}`;
    }

    // Conditionally add Content-Type only if the method usually requires it
    // const methodsWithBody = ['PUT', 'PATCH'];
    // if (methodsWithBody.includes(request.method.toUpperCase())) {
    //   headersConfig['Content-Type'] = 'application/json';
    // }

    // Apply headers in one clone
    const clonedRequest = request.clone({ setHeaders: headersConfig });

    return next.handle(clonedRequest);
  }
}
