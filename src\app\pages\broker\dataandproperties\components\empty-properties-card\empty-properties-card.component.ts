import { Component, Input } from '@angular/core';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-empty-properties-card',
  templateUrl: './empty-properties-card.component.html',
  styleUrl: './empty-properties-card.component.scss',
})
export class EmptyPropertiesCardComponent {
  @Input() userRole: string ;
  @Input() customMessage: string = '';
  @Input() onFileUpload!: (file: File) => void;
  @Input() onDownloadTemplate!: () => void;

  constructor(public translationService: TranslationService) {}

  get displayMessage(): string {
    if (this.customMessage) {
      return this.customMessage;
    }

    switch (this.userRole) {
      case 'broker':
        return this.getTranslatedText('BROKER_EMPTY_MESSAGE');
      case 'developer':
        return this.getTranslatedText('DEVELOPER_EMPTY_MESSAGE');
      default:
        return this.getTranslatedText('BROKER_EMPTY_MESSAGE');
    }
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'NOTHING_CURRENTLY': 'لا يوجد شيء حالياً!',
        'BROKER_EMPTY_MESSAGE': 'أنشئ عقار جديد أو ارفع وحدات العقارات التي تعمل عليها.',
        'DEVELOPER_EMPTY_MESSAGE': 'أنشئ مشروع جديد أو ابدأ في بناء محفظة التطوير الخاصة بك.',
        'UPLOAD_PROPERTY_UNITS': 'رفع ملف وحدات العقارات',
        'DOWNLOAD_TEMPLATE': 'تحميل قالب الإعلانات.xls'
      },
      'en': {
        'NOTHING_CURRENTLY': 'There is nothing currently!',
        'BROKER_EMPTY_MESSAGE': "Create a new property or upload the property units you're working on.",
        'DEVELOPER_EMPTY_MESSAGE': 'Create a new project or start building your development portfolio.',
        'UPLOAD_PROPERTY_UNITS': 'Upload Property Units File',
        'DOWNLOAD_TEMPLATE': 'Download Ads-template.xls'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }

  onFileSelected(event: any) {
    const file = event.target.files[0];
    if (file && this.onFileUpload) {
      this.onFileUpload(file);
    }
  }

  downloadTemplate() {
    if (this.onDownloadTemplate) {
      this.onDownloadTemplate();
    }
  }
}
