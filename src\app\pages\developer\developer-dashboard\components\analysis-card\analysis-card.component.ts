import { Component, Input } from '@angular/core';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-analysis-card',
  templateUrl: './analysis-card.component.html',
  styleUrls: ['./analysis-card.component.scss']
})
export class AnalysisCardComponent {

  @Input() backgroundColor: string = '';
  @Input() title: string = '';
  @Input() totalRequests: number = 0;
  @Input() activeRequests: number = 0;

  constructor(public translationService: TranslationService) {}

  getPercentage(): number {
    return Number((this.totalRequests > 0 ? (this.activeRequests / this.totalRequests) * 100 : 0).toFixed(2));
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'TOTAL_NUMBER': 'العدد الإجمالي',
        'ACTIVE': 'نشط'
      },
      'en': {
        'TOTAL_NUMBER': 'Total Number',
        'ACTIVE': 'Active'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }
}
