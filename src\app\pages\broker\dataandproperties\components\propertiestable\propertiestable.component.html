<div class="table-responsive mb-5">
  <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 mt-5">
    <thead>
      <tr class="fw-bold bg-light-dark-blue text-dark-blue me-1 ms-1">
        <!-- Base columns - always shown -->
        <th class="min-w-150px cursor-pointer ps-4 rounded-start" (click)="sortData('owner_name')"
          *ngIf="shouldShowColumn('ownerName')">
          {{ getTranslatedText('OWNER_NAME') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("owner_name")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('ownerPhone')" *ngIf="shouldShowColumn('ownerPhone')">
          {{ getTranslatedText('OWNER_PHONE') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("ownerPhone")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('type')" *ngIf="shouldShowColumn('type')">
          {{ getTranslatedText('UNIT_TYPE') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("type")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('city_id')" *ngIf="shouldShowColumn('city')">
          {{ getTranslatedText('CITY') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("city_id")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('area_id')" *ngIf="shouldShowColumn('area')">
          {{ getTranslatedText('AREA') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("area_id")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('sub_area')" *ngIf="shouldShowColumn('subArea')">
          {{ getTranslatedText('SUB_AREA') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("sub_area")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('detailedAddress')"
          *ngIf="shouldShowColumn('detailedAddress')">
          {{ getTranslatedText('DETAILED_ADDRESS') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("detailedAddress")
            }}</span>
        </th>
        <th class="min-w-150px" *ngIf="shouldShowColumn('location')">
          {{ getTranslatedText('LOCATION_ON_MAP') }}
        </th>

        <!-- Dynamic columns based on filter -->
        <th class="min-w-150px cursor-pointer" (click)="sortData('compound_name')"
          *ngIf="shouldShowColumn('compoundName')">
          {{ getTranslatedText('COMPOUND_NAME') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("compound_name")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('mall_name')" *ngIf="shouldShowColumn('mallName')">
          {{ getTranslatedText('MALL_NAME') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("mall_name")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('building_number')"
          *ngIf="shouldShowColumn('buildingNumber')">
          {{ getTranslatedText('BUILDING_NUMBER') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("building_number")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('unit_number')" *ngIf="shouldShowColumn('unitNumber')">
          {{ getTranslatedText('UNIT_NUMBER') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("unit_number")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('floor')" *ngIf="shouldShowColumn('floor')">
          {{ getTranslatedText('FLOOR') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("floor")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('unit_area')" *ngIf="shouldShowColumn('unitArea')">
          {{ getTranslatedText('UNIT_AREA') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("unit_area")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('ground_area')" *ngIf="shouldShowColumn('groundArea')">
          {{ getTranslatedText('GROUND_AREA') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("ground_area")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('building_area')"
          *ngIf="shouldShowColumn('buildingArea')">
          {{ getTranslatedText('BUILDING_AREA') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("building_area")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('number_of_rooms')"
          *ngIf="shouldShowColumn('numberOfRooms')">
          {{ getTranslatedText('ROOMS') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("number_of_rooms")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('number_of_bathrooms')"
          *ngIf="shouldShowColumn('numberOfBathrooms')">
          {{ getTranslatedText('BATHROOMS') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("number_of_bathrooms")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('number_of_floors')"
          *ngIf="shouldShowColumn('numberOfFloors')">
          {{ getTranslatedText('NUMBER_OF_FLOORS') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("number_of_floors")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('view')" *ngIf="shouldShowColumn('view')">
          {{ getTranslatedText('VIEW') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("view")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('finishing_type')"
          *ngIf="shouldShowColumn('finishingType')">
          {{ getTranslatedText('FINISHING_TYPE') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("finishing_type")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('unit_description')"
          *ngIf="shouldShowColumn('unitDescription')">
          {{ getTranslatedText('UNIT_DESCRIPTION') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("unit_description")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('unit_design')" *ngIf="shouldShowColumn('unitDesign')">
          {{ getTranslatedText('UNIT_DESIGN') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("unit_design")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('unit_facing')" *ngIf="shouldShowColumn('unitFacing')">
          {{ getTranslatedText('UNIT_FACING') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("unit_facing")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('delivery_status')"
          *ngIf="shouldShowColumn('deliveryStatus')">
          {{ getTranslatedText('DELIVERY_STATUS') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("delivery_status")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('fit_out_condition')"
          *ngIf="shouldShowColumn('fitOutCondition')">
          {{ getTranslatedText('FIT_OUT_CONDITION') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("fit_out_condition")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('building_deadline')"
          *ngIf="shouldShowColumn('buildingDeadline')">
          {{ getTranslatedText('BUILDING_DEADLINE') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("building_deadline")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('requested_over')"
          *ngIf="shouldShowColumn('requestedOver')">
          {{ getTranslatedText('REQUESTED_OVER') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("requested_over")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('furnishing_status')"
          *ngIf="shouldShowColumn('furnishingStatus')">
          {{ getTranslatedText('FURNISHING_STATUS') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("furnishing_status")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('legal_status')"
          *ngIf="shouldShowColumn('legalStatus')">
          {{ getTranslatedText('LEGAL_STATUS') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("legal_status")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('financial_status')"
          *ngIf="shouldShowColumn('financialStatus')">
          {{ getTranslatedText('FINANCIAL_STATUS') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("financial_status")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('activity')" *ngIf="shouldShowColumn('activity')">
          {{ getTranslatedText('ACTIVITY') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("activity")
            }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('payment_system')"
          *ngIf="shouldShowColumn('paymentSystem')">
          {{ getTranslatedText('PAYMENT_SYSTEM') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("payment_system")
            }}</span>
        </th>

        <th class="min-w-150px cursor-pointer" (click)="sortData('total_price')" *ngIf="shouldShowColumn('totalPrice')">
          {{ getTranslatedText('TOTAL_PRICE') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("total_price")
            }}</span>
        </th>

        <th class="min-w-150px cursor-pointer" (click)="sortData('rent_recurrence')"
          *ngIf="shouldShowColumn('rentRecurrence')">
          {{ getTranslatedText('RENT_RECURRENCE') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("rent_recurrence")
            }}</span>
        </th>
        <!-- Show Daily Rent header only when there are properties with daily rent -->
        <th class="min-w-150px cursor-pointer" (click)="sortData('daily_rent')"
          *ngIf="shouldShowColumn('dailyRent') && hasPropertiesWithDailyRent()">
          {{ getTranslatedText('DAILY_RENT') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("daily_rent")
            }}</span>
        </th>
        <!-- Show Monthly Rent header when there are properties with monthly/annually rent -->
        <th class="min-w-150px cursor-pointer" (click)="sortData('monthly_rent')"
          *ngIf="shouldShowColumn('monthlyRent') && hasPropertiesWithMonthlyRent()">
          {{ getTranslatedText('MONTHLY_RENT') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("monthly_rent")
            }}</span>
        </th>

        <th class="min-w-200px cursor-pointer" (click)="sortData('other_accessories')"
          *ngIf="shouldShowColumn('otherAccessories')">
          {{ getTranslatedText('OTHER_ACCESSORIES') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("other_accessories")
            }}</span>
        </th>

        <!-- Always show unit plan and actions -->
        <th class="min-w-150px" *ngIf="shouldShowColumn('unitPlan')">
          {{ getTranslatedText('UNIT_PLAN') }}
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('status')" *ngIf="shouldShowColumn('status')">
          {{ getTranslatedText('STATUS') }}
          <span class="ms-1 text-primary fw-bold">{{
            getSortArrow("status")
            }}</span>
        </th>
        <th class="min-w-50px text-end rounded-end pe-4" *ngIf="shouldShowColumn('actions')">
          {{ getTranslatedText('ACTIONS') }}
        </th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let property of rows">
        <!-- Base columns - always shown -->
        <td *ngIf="shouldShowColumn('ownerName')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6 ps-4">
            {{ property.ownerName }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('ownerPhone')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.ownerPhone }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('type')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.type }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('city')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.city?.name_en }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('area')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.area?.name_en }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('subArea')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property?.subArea?.name_en || getTranslatedText('UNKNOWN') }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('detailedAddress')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.detailedAddress }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('location')">
          <button class="btn btn-icon btn-sm btn-light-primary" data-bs-toggle="tooltip"
            [title]="getTranslatedText('VIEW_ON_MAP')" (click)="showImageModal(property.location)">
            <i class="fa-solid fa-map-location-dot"></i>
          </button>
        </td>

        <!-- Dynamic columns based on filter -->
        <td *ngIf="shouldShowColumn('compoundName')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.compoundName }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('mallName')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.additionalDetails?.mallName }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('buildingNumber')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.buildingNumber }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('unitNumber')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.unitNumber }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('floor')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.floor }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('unitArea')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.unitArea }} m²
          </span>
        </td>
        <td *ngIf="shouldShowColumn('groundArea')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.additionalDetails?.groundArea }} m²
          </span>
        </td>
        <td *ngIf="shouldShowColumn('buildingArea')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.additionalDetails?.buildingArea }} m²
          </span>
        </td>
        <td *ngIf="shouldShowColumn('numberOfRooms')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.numberOfRooms }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('numberOfBathrooms')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.numberOfBathrooms }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('numberOfFloors')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.additionalDetails?.numberOfFloors }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('view')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.view }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('finishingType')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.finishingType }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('unitDescription')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.additionalDetails?.unitDescription }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('unitDesign')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.additionalDetails?.unitDesign }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('unitFacing')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.additionalDetails?.unitFacing }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('deliveryStatus')">
          <span class="badge badge-light-success">{{
            property.deliveryStatus
            }}</span>
        </td>
        <td *ngIf="shouldShowColumn('fitOutCondition')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.additionalDetails?.fitOutCondition }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('buildingDeadline')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.additionalDetails?.buildingDeadline }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('requestedOver')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.requestedOver }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('furnishingStatus')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">{{
            property.additionalDetails?.furnishingStatus
            }}</span>
        </td>
        <td *ngIf="shouldShowColumn('legalStatus')">
          <span class="badge badge-light-info">{{ property.additionalDetails?.legalStatus }}</span>
        </td>
        <td *ngIf="shouldShowColumn('financialStatus')">
          <span class="badge badge-light-warning">{{
            property.additionalDetails?.financialStatus
            }}</span>
        </td>
        <td *ngIf="shouldShowColumn('activity')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.additionalDetails?.activity }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('paymentSystem')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.paymentSystem }}
          </span>
        </td>

        <td *ngIf="shouldShowColumn('totalPrice')">
          <!-- Single payment system -->
          <span *ngIf="property.paymentSystem !== 'all_of_the_above_are_suitable'" class="fw-bold d-block mb-1 fs-6"
            [ngClass]="{
              'text-success': property.paymentSystem === 'cash',
              'text-primary': property.paymentSystem === 'installment'
            }">
            <ng-container *ngIf="property.paymentSystem === 'cash'">
              {{
              property.totalPriceInCash
              | currency : "EGP" : "symbol" : "1.0-0"
              }}
            </ng-container>
            <ng-container *ngIf="property.paymentSystem === 'installment'">
              {{
              property.totalPriceInInstallment
              | currency : "EGP" : "symbol" : "1.0-0"
              }}
            </ng-container>
          </span>

          <!-- Both payment systems -->
          <div *ngIf="property.paymentSystem === 'all_of_the_above_are_suitable'" class="d-flex flex-column gap-1">
            <span class="text-success fw-bold fs-6">
              {{ getTranslatedText('CASH') }}:
              {{
              property.totalPriceInCash
              | currency : "EGP" : "symbol" : "1.0-0"
              }}
            </span>
            <span class="text-primary fw-bold fs-6">
              {{ getTranslatedText('INSTALLMENT') }}:
              {{
              property.totalPriceInInstallment
              | currency : "EGP" : "symbol" : "1.0-0"
              }}
            </span>
          </div>
        </td>
        <td *ngIf="shouldShowColumn('rentRecurrence')">
          <span class="badge badge-light-primary">{{
            property.additionalDetails?.rentRecurrence
            }}</span>
        </td>
        <!-- Show Daily Rent column when there are properties with daily rent -->
        <td *ngIf="shouldShowColumn('dailyRent') && hasPropertiesWithDailyRent()">
          <span *ngIf="property.additionalDetails?.rentRecurrence === 'daily'"
            class="text-success fw-bold d-block mb-1 fs-6">
            {{ property.dailyRent | currency : "EGP" : "symbol" : "1.0-0" }}
          </span>
          <span *ngIf="property.additionalDetails?.rentRecurrence !== 'daily'" class="text-muted">-</span>
        </td>
        <!-- Show Monthly Rent column when there are properties with monthly/annually rent -->
        <td *ngIf="shouldShowColumn('monthlyRent') && hasPropertiesWithMonthlyRent()">
          <span
            *ngIf="property.additionalDetails?.rentRecurrence === 'monthly' || property.additionalDetails?.rentRecurrence === 'annually'"
            class="text-success fw-bold d-block mb-1 fs-6">
            {{ property.monthlyRent | currency : "EGP" : "symbol" : "1.0-0" }}
          </span>
          <span
            *ngIf="property.additionalDetails?.rentRecurrence !== 'monthly' && property.additionalDetails?.rentRecurrence !== 'annually'"
            class="text-muted">-</span>
        </td>

        <td *ngIf="shouldShowColumn('otherAccessories')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.otherAccessories }}
          </span>
        </td>

        <!-- Always show unit plan and actions -->
        <td *ngIf="shouldShowColumn('unitPlan')">
          <button class="btn btn-sm btn-light-info" (click)="showUnitPlanModal(property.diagram)">
            <i class="fa-solid fa-file-image me-1"></i> {{ getTranslatedText('VIEW_PLAN') }}
          </button>
        </td>
        <td *ngIf="shouldShowColumn('status')">
          <span class="badge fw-bold" [ngClass]="{
              'badge-light-success': property.status === 'available',
              'badge-light-danger': property.status === 'sold',
              'badge-light-warning': property.status === 'reserved',
              'badge-light-info': property.status === 'pending',
              'badge-light-primary': property.status === 'new'
            }">
            {{ property.status }}
          </span>
        </td>
        <td class="text-end pe-4" *ngIf="shouldShowColumn('actions')">
          <div class="dropdown">
            <button class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary" type="button"
              data-bs-toggle="dropdown">
              <i class="fa-solid fa-ellipsis-vertical"></i>
            </button>
            <ul class="dropdown-menu">
              <li>
                <button class="dropdown-item" (click)="viewProperty(property)">
                  <i class="fa-solid fa-eye me-2"></i> {{ getTranslatedText('VIEW_UNIT_DETAILS') }}
                </button>
              </li>
              <li *ngIf="!property.isAdvertisement">
                <button class="dropdown-item" (click)="makeAdd(property.id)">
                   Make As Add
                </button>
              </li>
              <li *ngIf="property.status != 'sold'">
                <button class="dropdown-item" (click)="makeSold(property.id)">
                  Make Sold
                </button>
              </li>
            </ul>
          </div>
        </td>
      </tr>
    </tbody>
  </table>
  <div class="m-2">
    <app-pagination [totalItems]="page.totalElements" [itemsPerPage]="page.size" [currentPage]="page.pageNumber"
      (pageChange)="onPageChange($event)">
    </app-pagination>
  </div>
</div>

<app-view-apartment-model [selectedUnitPlanImage]="selectedUnitPlanImage"></app-view-apartment-model>
