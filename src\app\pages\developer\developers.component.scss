.upload-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(46, 42, 126, 0.1);
  margin: 0 auto;
}

.cursor-pointer {
  cursor: pointer;
}

.border-primary {
  border-color: #2e2a7e !important;
}

.text-primary {
  color: #2e2a7e !important;
}

label.cursor-pointer {
  cursor: pointer;
  transition: all 0.2s ease;
}

// label.cursor-pointer:hover {
//   background-color: rgba(46, 42, 126, 0.05);
// }

.badge.bg-success {
  background-color: #50cd89 !important;
}

/* Developer Page RTL Support - Enhanced like All Brokers */

// RTL Table Support - Enhanced like All Brokers
.rtl-table {
  direction: rtl;

  .table {
    th, td {
      text-align: right;
    }

    .d-flex {
      &.flex-row-reverse {
        flex-direction: row-reverse !important;
      }
    }
  }
}

// Arabic Font Support - Enhanced like All Brokers
[style*="direction: rtl"] {
  .table {
    th, td {
      text-align: right !important;
      padding: 12px 16px;
    }

    .symbol {
      margin-left: 12px;
      margin-right: 0;
    }

    .badge {
      text-align: center;
    }
  }
}

/* Developer Page RTL Support - Simplified */

// RTL Support for Arabic Language
html[lang="ar"] {

  // Main container fixes
  .d-flex.justify-content-between {
    flex-direction: row-reverse !important;
  }

  // Search input fixes
  .form-control {
    direction: rtl !important;
    text-align: right !important;

    &.form-control-flush {
      padding-right: 2.5rem !important;
      padding-left: 0.75rem !important;
    }
  }

  // Search icon position
  .position-relative {
    app-keenicon,
    .keenicon {
      right: 12px !important;
      left: auto !important;
    }
  }

  // Navigation tabs
  .nav-stretch {
    flex-direction: row-reverse !important;

    .nav-item {
      .nav-link {
        margin-left: 0.5rem !important;
        margin-right: 0 !important;
      }
    }
  }

  // Table alignment
  .table {
    th, td {
      text-align: right !important;

      &:last-child {
        text-align: left !important;
      }
    }
  }

  // Button spacing
  .btn {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
  }
}

// Enhanced styling for Arabic - Clean version
:host-context(html[lang="ar"]) {
  .btn {
    font-size: 1rem;
    font-weight: 600;
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .nav-link.btn {
    font-size: 1rem;
    font-weight: 600;
    min-width: 140px;
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .modal-title {
    font-size: 1.3rem;
    font-weight: 700;
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  h1, h4, h5 {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .form-control {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .table {
    th, td {
      font-family: 'Hacen Liner Screen St', sans-serif;
    }
  }

  .badge {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .text-muted {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .dropdown-item {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .modal-body {
    p, span {
      font-family: 'Hacen Liner Screen St', sans-serif;
    }
  }

  // إصلاح ترتيب الصورة والنص في الجدول
  .table {
    .d-flex.align-items-center {
      flex-direction: row !important; // الصورة أولاً ثم النص

      .symbol {
        margin-left: 0 !important;
        margin-right: 0.75rem !important;
      }

      .d-flex.flex-column {
        text-align: right !important;
      }
    }
  }
}

// Responsive adjustments for Arabic
@media (max-width: 768px) {
  html[lang="ar"] {
    .nav-link.btn {
      min-width: 100px;
      font-size: 0.8rem;
    }

    .btn {
      font-size: 0.8rem;
      padding: 0.375rem 0.75rem;
    }
  }
}

// إصلاحات إضافية للتصميم العربي
.developers-page {

  // تحسين شكل البحث
  .position-relative {
    .form-control {
      border: 1px solid #e1e5e9 !important;
      box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;

      &:focus {
        border-color: #80bdff !important;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
      }
    }

    app-keenicon {
      z-index: 10;
    }
  }

  // تحسين شكل الأزرار
  .nav-link.btn {
    border: 1px solid transparent;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
  }

  // تحسين المسافات
  .d-flex.justify-content-between {
    gap: 1rem;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: center;
      text-align: center;
    }
  }
}

// إصلاحات خاصة للجدول في العربية
:host-context(html[lang="ar"]) {
  .table {
    // إصلاح Headers
    th {
      text-align: center !important;
      vertical-align: middle !important;
      padding: 1rem 0.75rem !important;

      &:first-child {
        text-align: right !important;
        padding-right: 1rem !important;
      }

      &:last-child {
        text-align: left !important;
        padding-left: 1rem !important;
      }
    }

    // إصلاح Cells
    td {
      text-align: center !important;
      vertical-align: middle !important;
      padding: 1rem 0.75rem !important;

      &:first-child {
        text-align: right !important;
        padding-right: 1rem !important;
      }

      &:last-child {
        text-align: left !important;
        padding-left: 1rem !important;
      }
    }

    // إصلاح ترتيب الصورة والنص
    .d-flex.align-items-center {
      justify-content: center !important;

      .symbol {
        margin-right: 0.75rem !important;
        margin-left: 0 !important;
      }

      .d-flex.flex-column {
        text-align: right !important;
      }
    }

    // إصلاح القائمة المنسدلة
    .dropdown {
      .dropdown-menu {
        right: 0 !important;
        left: auto !important;

        .dropdown-item {
          text-align: right !important;
          flex-direction: row-reverse !important;

          i {
            margin-right: 0 !important;
            margin-left: 0.5rem !important;
          }
        }
      }
    }
  }
}
