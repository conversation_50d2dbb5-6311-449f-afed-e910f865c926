// All Brokers component styles
.table {
  th {
    font-weight: 600;
    color: #0D47A1;
    border-bottom: 2px solid #f1f1f1;
    padding: 12px 16px;
  }

  td {
    vertical-align: middle;
    border-bottom: 1px solid #f1f1f1;
    padding: 12px 16px;
  }
}

// RTL Table Support
.rtl-table {
  direction: rtl;

  .table {
    th, td {
      text-align: right;
    }

    .d-flex {
      &.flex-row-reverse {
        flex-direction: row-reverse !important;
      }
    }
  }
}

// Arabic Font Support
[style*="direction: rtl"] {
  .table {
    th, td {
      text-align: right !important;
    }

    .symbol {
      margin-left: 12px;
      margin-right: 0;
    }

    .badge {
      text-align: center;
    }
  }
}

.symbol {
  .symbol-label {
    font-size: 1rem;
    font-weight: 600;
  }
}

.btn {
  &.btn-sm {
    padding: 6px 12px;
    font-size: 0.875rem;
  }
}

.badge {
  &.badge-light-success {
    background-color: rgba(34, 197, 94, 0.1);
    color: #22c55e;
  }

  &.badge-light-warning {
    background-color: rgba(251, 191, 36, 0.1);
    color: #fbbf24;
  }

  &.badge-light-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
  }

  &.badge-light-info {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
  }
}

// RTL Support
.rtl-layout {
  direction: rtl;
  text-align: right;

  .card-header {
    .d-flex {
      flex-direction: row-reverse;
    }
  }

  .table {
    th, td {
      text-align: right;
    }

    .form-check {
      text-align: center;
    }
  }

  .d-flex {
    &.justify-content-between {
      flex-direction: row-reverse;
    }

    &.align-items-center {
      flex-direction: row-reverse;
    }
  }

  .symbol {
    margin-right: 0;
    margin-left: 1rem;
  }

  .btn {
    .fa-solid {
      margin-right: 0;
      margin-left: 0.5rem;
    }
  }

  .form-control {
    text-align: right;
    padding-right: 2.5rem;
    padding-left: 0.75rem;
  }

  .position-absolute {
    &.top-50 {
      right: 0.75rem;
      left: auto;
    }
  }

  .fas.fa-star {
    margin-right: 0;
    margin-left: 0.5rem;
  }
}

.rtl-table {
  .table {
    th:first-child {
      border-radius: 0 0.375rem 0.375rem 0;
    }

    th:last-child {
      border-radius: 0.375rem 0 0 0.375rem;
    }
  }
}

// Arabic Fonts Support
@import url('https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@100;200;300;400;500;600;700;800;900&display=swap');

.arabic-font {
  font-family: 'Noto Kufi Arabic', sans-serif;
}

.arabic-text {
  font-family: 'Hacen Liner Screen', sans-serif;
  line-height: 1.8;
}
