<div class="container-fluid px-4 py-5" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
  <!-- Header -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card border-0 shadow-sm">
        <div class="card-body p-4">
          <div class="d-flex align-items-center justify-content-between w-100">

            <!-- للعربية: الزر أولاً (أقصى الشمال) -->
            <div *ngIf="translationService.isRTL()" class="order-1">
              <button class="btn btn-primary btn-sm" (click)="refresh()"
                [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                <i class="ki-duotone ki-arrows-circle fs-4 ms-2">
                  <span class="path1"></span>
                  <span class="path2"></span>
                </i>
                {{ 'SUPER_ADMIN.DASHBOARD.CLIENTS.REFRESH_DATA' | translate }}
              </button>
            </div>

            <!-- المحتوى -->
            <div [class.text-end]="translationService.isRTL()" [class.text-start]="!translationService.isRTL()"
              [class.order-2]="translationService.isRTL()">
              <div class="d-flex align-items-center mb-2" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'"
                [style.gap]="translationService.isRTL() ? '1rem' : '0.75rem'">
                <div class="symbol symbol-40px" [class.me-4]="!translationService.isRTL()"
                  [class.ms-4]="translationService.isRTL()" [style.flex-shrink]="'0'">
                  <div class="symbol-label bg-light-primary">
                    <i class="ki-duotone ki-profile-circle fs-3 text-primary">
                      <span class="path1"></span>
                      <span class="path2"></span>
                      <span class="path3"></span>
                    </i>
                  </div>
                </div>
                <div [style.flex]="'1'" [style.min-width]="'0'">
                  <h1 class="text-gray-900 fw-bold mb-0 fs-2"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.margin-right]="translationService.isRTL() ? '0.5rem' : '0'"
                    [style.margin-left]="!translationService.isRTL() ? '0.5rem' : '0'"
                    [style.margin-bottom]="'0.25rem'">
                    {{ 'SUPER_ADMIN.DASHBOARD.CLIENTS.TITLE' | translate }}
                  </h1>
                  <p class="text-muted mb-0 fs-6"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.margin-right]="translationService.isRTL() ? '0.5rem' : '0'"
                    [style.margin-left]="!translationService.isRTL() ? '0.5rem' : '0'">
                    {{ 'SUPER_ADMIN.DASHBOARD.CLIENTS.DESCRIPTION' | translate }}
                  </p>
                </div>
              </div>
            </div>

            <!-- للإنجليزية: الزر آخراً (اليمين) -->
            <div *ngIf="!translationService.isRTL()">
              <button class="btn btn-primary btn-sm" (click)="refresh()">
                <i class="ki-duotone ki-arrows-circle fs-4 me-2">
                  <span class="path1"></span>
                  <span class="path2"></span>
                </i>
                {{ 'SUPER_ADMIN.DASHBOARD.CLIENTS.REFRESH_DATA' | translate }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Statistics Cards -->
  <div class="row g-4 mb-5">
    <div class="col-lg-3 col-md-6">
      <div class="card border-0 shadow-sm h-100">
        <div class="card-body p-4">
          <div class="d-flex align-items-center" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <div class="symbol" [class.symbol-60px]="!translationService.isRTL()"
              [class.symbol-45px]="translationService.isRTL()" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <div class="symbol-label bg-light-primary">
                <i class="ki-duotone ki-people text-primary" [class.fs-2]="!translationService.isRTL()"
                  [class.fs-3]="translationService.isRTL()">
                  <span class="path1"></span>
                  <span class="path2"></span>
                  <span class="path3"></span>
                  <span class="path4"></span>
                  <span class="path5"></span>
                </i>
              </div>
            </div>
            <div [class.text-end]="translationService.isRTL()" [class.text-start]="!translationService.isRTL()">
              <div class="fs-2 fw-bold text-gray-900 mb-1">
                {{ formatNumber(totalClients) | arabicNumbers }}
              </div>
              <div class="fs-7 text-muted"
                [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                {{ 'SUPER_ADMIN.DASHBOARD.CLIENTS.TOTAL_CLIENTS' | translate }}
              </div>
              <div class="fs-8 text-muted"
                [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                {{ totalComplaints | arabicNumbers }} {{ 'SUPER_ADMIN.DASHBOARD.CLIENTS.COMPLAINTS' | translate }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-3 col-md-6">
      <div class="card border-0 shadow-sm h-100">
        <div class="card-body p-4">
          <div class="d-flex align-items-center" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <div class="symbol symbol-60px" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <div class="symbol-label bg-light-warning">
                <i class="ki-duotone ki-document fs-2 text-warning">
                  <span class="path1"></span>
                  <span class="path2"></span>
                </i>
              </div>
            </div>
            <div [class.text-end]="translationService.isRTL()" [class.text-start]="!translationService.isRTL()">
              <div class="fs-2 fw-bold text-gray-900 mb-1">
                {{ formatNumber(totalRequests) }}
              </div>
              <div class="fs-7 text-muted"
                [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                {{ 'SUPER_ADMIN.DASHBOARD.CLIENTS.TOTAL_REQUESTS' | translate }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-3 col-md-6">
      <div class="card border-0 shadow-sm h-100">
        <div class="card-body p-4">
          <div class="d-flex align-items-center" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <div class="symbol symbol-60px" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <div class="symbol-label bg-light-info">
                <i class="ki-duotone ki-chart-simple fs-2 text-info">
                  <span class="path1"></span>
                  <span class="path2"></span>
                  <span class="path3"></span>
                  <span class="path4"></span>
                </i>
              </div>
            </div>
            <div [class.text-end]="translationService.isRTL()" [class.text-start]="!translationService.isRTL()">
              <div class="fs-2 fw-bold text-gray-900 mb-1">
                {{ formatNumber(totalVisited) }}
              </div>
              <div class="fs-7 text-muted"
                [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                {{ 'SUPER_ADMIN.DASHBOARD.CLIENTS.TOTAL_VISITED' | translate }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-3 col-md-6">
      <div class="card border-0 shadow-sm h-100">
        <div class="card-body p-4">
          <div class="d-flex align-items-center" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <div class="symbol" [class.symbol-60px]="!translationService.isRTL()"
              [class.symbol-45px]="translationService.isRTL()" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <div class="symbol-label bg-light-success">
                <i class="ki-duotone ki-check-circle text-success" [class.fs-2]="!translationService.isRTL()"
                  [class.fs-3]="translationService.isRTL()">
                  <span class="path1"></span>
                  <span class="path2"></span>
                </i>
              </div>
            </div>
            <div [class.text-end]="translationService.isRTL()" [class.text-start]="!translationService.isRTL()">
              <div class="fs-2 fw-bold text-gray-900 mb-1">
                {{ formatNumber(totalRequests) | arabicNumbers }}
              </div>
              <div class="fs-7 text-muted"
                [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                {{ 'SUPER_ADMIN.DASHBOARD.CLIENTS.ACTIVE_REQUESTS' | translate }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Top Clients and Request Stats -->
  <!-- <div class="row g-6 mb-8"> -->
  <!-- Top Clients -->
  <!-- <div class="col-lg-8">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-star me-2"></i>
            أفضل العملاء
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="table-responsive">
            <table class="table table-row-bordered align-middle gs-0 gy-3">
              <thead>
                <tr class="fw-bold text-muted bg-light">
                  <th class="ps-4 rounded-start">العميل</th>
                  <th>الطلبات</th>
                  <th>الميزانية الإجمالية</th>
                  <th>المنطقة المفضلة</th>
                  <th class="rounded-end">الحالة</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let client of topClients; let i = index">
                  <td class="ps-4">
                    <div class="d-flex align-items-center">
                      <div class="symbol symbol-40px me-3">
                        <div class="symbol-label bg-light-primary">
                          <span class="text-primary fw-bold">{{ i + 1 }}</span>
                        </div>
                      </div>
                      <div>
                        <span class="text-dark fw-bold d-block">{{ client.name }}</span>
                        <span class="text-muted fs-7">{{ client.phone }}</span>
                      </div>
                    </div>
                  </td>
                  <td>
                    <span class="badge badge-light-primary">{{ client.requests }}</span>
                  </td>
                  <td>
                    <span class="text-success fw-bold">{{ formatPrice(client.totalBudget) }}</span>
                  </td>
                  <td>
                    <span class="text-muted">{{ client.preferredArea }}</span>
                  </td>
                  <td>
                    <span class="badge" [ngClass]="'badge-light-' + getStatusColor(client.status)">
                      {{ client.status }}
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div> -->

  <!-- Request Statistics -->
  <!-- <div class="col-lg-4">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-chart-pie me-2"></i>
            إحصائيات الطلبات
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="table-responsive">
            <table class="table table-row-bordered align-middle gs-0 gy-3">
              <thead>
                <tr class="fw-bold text-muted bg-light">
                  <th class="ps-4 rounded-start">الحالة</th>
                  <th>العدد</th>
                  <th class="rounded-end">النسبة</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let request of requestStats">
                  <td class="ps-4">
                    <span class="text-dark fw-bold">{{ request.status }}</span>
                  </td>
                  <td>
                    <span class="badge" [ngClass]="'badge-light-' + getStatusColor(request.status)">
                      {{ request.count }}
                    </span>
                  </td>
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="progress h-6px w-60px me-2">
                        <div class="progress-bar" [ngClass]="'bg-' + getStatusColor(request.status)"
                          [style.width.%]="request.percentage"></div>
                      </div>
                      <span class="text-muted fs-7">{{ request.percentage }}%</span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div> -->
  <!-- </div> -->

  <!-- Client Performance -->
  <div class="row mb-5" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
    <div class="col-12">
      <div class="card border-0 shadow-sm" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
        <div class="card-header border-0 py-4" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
          <div class="d-flex align-items-center w-100" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'"
            [style.justify-content]="translationService.isRTL() ? 'flex-end !important' : 'flex-start !important'"
            [class.justify-content-end-rtl]="translationService.isRTL()"
            [class.justify-content-start-ltr]="!translationService.isRTL()">
            <div class="symbol symbol-40px" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <div class="symbol-label bg-light-primary">
                <i class="ki-duotone ki-chart-simple fs-3 text-primary">
                  <span class="path1"></span>
                  <span class="path2"></span>
                  <span class="path3"></span>
                  <span class="path4"></span>
                </i>
              </div>
            </div>
            <h3 class="card-title text-gray-900 fw-bold fs-3 mb-0"
              [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ 'SUPER_ADMIN.DASHBOARD.CLIENTS.REQUEST_TYPES' | translate }}
            </h3>
          </div>
        </div>
        <div class="card-body p-4" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
          <div class="table-responsive" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <table class="table table-hover align-middle" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'"
              [class]="translationService.isRTL() ? 'table-rtl' : 'table-ltr'">
              <thead class="table-light">
                <tr>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.CLIENTS.REQUEST_TYPE' | translate }}
                  </th>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.CLIENTS.TOTAL' | translate }}
                  </th>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.CLIENTS.PERCENTAGE' | translate }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let type of requestTypeStats">
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="text-gray-900 fw-semibold fs-6"
                      [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                      {{ type.operation }}
                    </span>
                  </td>
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="badge badge-light-primary fs-7">{{ type.count }}</span>
                  </td>
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="text-gray-700 fs-6"
                      [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                      {{ type.percentage }}%
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Request Stats and Most Requested Units -->
  <div class="row g-4 mb-5" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
    <!-- Most Requested Units -->
    <div class="col-lg-6">
      <div class="card border-0 shadow-sm h-100" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
        <div class="card-header border-0 py-4" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
          <div class="d-flex align-items-center w-100" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'"
            [style.justify-content]="translationService.isRTL() ? 'flex-end !important' : 'flex-start !important'"
            [class.justify-content-end-rtl]="translationService.isRTL()"
            [class.justify-content-start-ltr]="!translationService.isRTL()">
            <div class="symbol symbol-40px" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <div class="symbol-label bg-light-success">
                <i class="ki-duotone ki-home fs-3 text-success">
                  <span class="path1"></span>
                  <span class="path2"></span>
                </i>
              </div>
            </div>
            <h3 class="card-title text-gray-900 fw-bold fs-4 mb-0"
              [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ 'SUPER_ADMIN.DASHBOARD.CLIENTS.MOST_REQUESTED_UNITS' | translate }}
            </h3>
          </div>
        </div>
        <div class="card-body p-4" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
          <div class="table-responsive" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <table class="table table-hover align-middle" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'"
              [class]="translationService.isRTL() ? 'table-rtl' : 'table-ltr'">
              <thead class="table-light">
                <tr>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.CLIENTS.AREA' | translate }}
                  </th>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.CLIENTS.TYPE' | translate }}
                  </th>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.CLIENTS.COUNT' | translate }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let request of mostRequests">
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="text-gray-900 fw-semibold fs-6"
                      [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                      {{ request.area }}
                    </span>
                  </td>
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="text-gray-700 fs-6"
                      [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                      {{ request.type }}
                    </span>
                  </td>
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="badge badge-light-success fs-7">{{ request.count }}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Geographic Distribution and Satisfaction -->
    <!-- <div class="row g-6 mb-8"> -->
    <!-- Geographic Distribution -->
    <!-- <div class="col-lg-6">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-map-marker-alt me-2"></i>
            التوزيع الجغرافي
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="table-responsive">
            <table class="table table-row-bordered align-middle gs-0 gy-3">
              <thead>
                <tr class="fw-bold text-muted bg-light">
                  <th class="ps-4 rounded-start">المنطقة</th>
                  <th>العملاء</th>
                  <th class="rounded-end">النسبة</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let area of geographicStats">
                  <td class="ps-4">
                    <span class="text-dark fw-bold">{{ area.area }}</span>
                  </td>
                  <td>
                    <span class="badge badge-light-info">{{ area.clients }}</span>
                  </td>
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="progress h-6px w-100px me-2">
                        <div class="progress-bar bg-info" [style.width.%]="area.percentage"></div>
                      </div>
                      <span class="text-muted fs-7">{{ area.percentage }}%</span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div> -->

    <!-- Customer Satisfaction -->
    <!-- <div class="col-lg-6">
      <div class="card card-flush h-100">
        <div class="card-header border-0 pt-6">
          <h3 class="card-title text-dark-blue fw-bold">
            <i class="fas fa-smile me-2"></i>
            رضا العملاء
          </h3>
        </div>
        <div class="card-body pt-0">
          <div class="row g-3">
            <div class="col-6" *ngFor="let key of ['veryHappy', 'happy', 'neutral', 'unhappy', 'veryUnhappy']">
              <div class="p-3 rounded text-center" [ngClass]="'bg-light-' + getSatisfactionColor(key)">
                <div class="fs-6 fw-bold" [ngClass]="'text-' + getSatisfactionColor(key)">
                  {{ getSatisfactionLabel(key) }}
                </div>
                <div class="fs-2 fw-bolder text-dark">{{ satisfactionStats[key] }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div> -->
    <!-- </div> -->

    <!-- Monthly Activity -->
    <div class="col-lg-6">
      <div class="card border-0 shadow-sm h-100" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
        <div class="card-header border-0 py-4" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
          <div class="d-flex align-items-center w-100" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'"
            [style.justify-content]="translationService.isRTL() ? 'flex-end !important' : 'flex-start !important'"
            [class.justify-content-end-rtl]="translationService.isRTL()"
            [class.justify-content-start-ltr]="!translationService.isRTL()">
            <div class="symbol symbol-40px" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <div class="symbol-label bg-light-info">
                <i class="ki-duotone ki-calendar-tick fs-3 text-info">
                  <span class="path1"></span>
                  <span class="path2"></span>
                  <span class="path3"></span>
                  <span class="path4"></span>
                  <span class="path5"></span>
                  <span class="path6"></span>
                </i>
              </div>
            </div>
            <h3 class="card-title text-gray-900 fw-bold fs-4 mb-0"
              [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ 'SUPER_ADMIN.DASHBOARD.CLIENTS.REGISTERED_MONTHLY_ACTIVITY' | translate }}
            </h3>
          </div>
        </div>
        <div class="card-body p-4" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
          <div class="table-responsive" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <table class="table table-hover align-middle" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'"
              [class]="translationService.isRTL() ? 'table-rtl' : 'table-ltr'">
              <thead class="table-light">
                <tr>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.CLIENTS.MONTH' | translate }}
                  </th>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.CLIENTS.NUMBER_OF_CLIENTS' | translate }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let month of RegisteredMonthlyActivity">
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="text-gray-900 fw-semibold fs-6"
                      [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                      {{ month.date }}
                    </span>
                  </td>
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="badge badge-light-info fs-7">{{ month.count }}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row g-4 mb-5" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
    <!-- Visited Monthly Activity -->
    <div class="col-lg-6">
      <div class="card border-0 shadow-sm h-100" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
        <div class="card-header border-0 py-4" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
          <div class="d-flex align-items-center w-100" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'"
            [style.justify-content]="translationService.isRTL() ? 'flex-end !important' : 'flex-start !important'"
            [class.justify-content-end-rtl]="translationService.isRTL()"
            [class.justify-content-start-ltr]="!translationService.isRTL()">
            <div class="symbol" [class.symbol-40px]="!translationService.isRTL()"
              [class.symbol-35px]="translationService.isRTL()" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <div class="symbol-label bg-light-warning">
                <i class="ki-duotone ki-star text-warning" [class.fs-3]="!translationService.isRTL()"
                  [class.fs-4]="translationService.isRTL()">
                  <span class="path1"></span>
                  <span class="path2"></span>
                </i>
              </div>
            </div>
            <h3 class="card-title text-gray-900 fw-bold fs-4 mb-0"
              [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ 'SUPER_ADMIN.DASHBOARD.CLIENTS.VISITED_MONTHLY_ACTIVITY' | translate }}
            </h3>
          </div>
        </div>
        <div class="card-body p-4" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
          <div class="table-responsive" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <table class="table table-hover align-middle" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'"
              [class]="translationService.isRTL() ? 'table-rtl' : 'table-ltr'">
              <thead class="table-light">
                <tr>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.CLIENTS.MONTH' | translate }}
                  </th>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.CLIENTS.NUMBER_OF_VISITORS' | translate }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let month of visitedMonthlyActivity">
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="text-gray-900 fw-semibold fs-6"
                      [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                      {{ month.date }}
                    </span>
                  </td>
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="badge badge-light-warning fs-7">{{ month.count }}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>