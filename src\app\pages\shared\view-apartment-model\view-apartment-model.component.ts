import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-view-apartment-model',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './view-apartment-model.component.html',
  styleUrl: './view-apartment-model.component.scss',
})
export class ViewApartmentModelComponent {
  @Input() selectedUnitPlanImage: string | null = null;

  constructor(public translationService: TranslationService) {}

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'UNIT_PLAN': 'مخطط الوحدة',
        'UNIT_PLAN_IMAGE': 'صورة مخطط الوحدة',
        'NO_IMAGE_AVAILABLE': 'لا توجد صورة متاحة',
        'CLOSE': 'إغلاق'
      },
      'en': {
        'UNIT_PLAN': 'Unit Plan',
        'UNIT_PLAN_IMAGE': 'Unit Plan Image',
        'NO_IMAGE_AVAILABLE': 'No image available',
        'CLOSE': 'Close'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }
}
