// RTL Support for Contract Requests Chart
.rtl-layout {
  direction: rtl;
  text-align: right;

  .card-title {
    text-align: right;

    .fs-1 {
      font-family: 'Hacen Liner Screen St', sans-serif;
      font-size: 1.3rem !important;
    }

    .fs-6 {
      font-family: 'Hacen Liner Screen St', sans-serif;
    }
  }

  .d-flex.fw-semibold.align-items-center {
    flex-direction: row-reverse;

    .text-gray-500 {
      font-family: 'Hacen Liner Screen St', sans-serif;
      text-align: right;
      margin-right: 0 !important;
      margin-left: 1rem !important;
    }

    .fw-bolder {
      font-family: 'Hacen Liner Screen St', sans-serif;
    }

    .bullet {
      margin-right: 0 !important;
      margin-left: 0.75rem !important;
    }
  }
}

// Enhanced styling for Arabic
:host-context(html[lang="ar"]) {
  .fs-1 {
    font-family: 'Hacen Liner Screen St', sans-serif !important;
    font-size: 1.3rem !important;
    font-weight: 700;
  }

  .fs-6 {
    font-family: 'Hacen Liner Screen St', sans-serif !important;
    font-size: 0.9rem !important;
  }

  .text-gray-500, .fw-bolder {
    font-family: 'Hacen Liner Screen St', sans-serif !important;
  }
}
