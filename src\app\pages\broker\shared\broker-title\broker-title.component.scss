// Arabic language specific styles
:host-context(.rtl) {
  .profile-image-arabic {
    width: 85px !important;
    height: 85px !important;

    img {
      width: 100% !important;
      height: 100% !important;
      object-fit: cover;
    }
  }

  .button-container-arabic {
    text-align: left !important;
    margin-left: 0 !important;
    margin-right: auto !important;
    display: flex !important;
    justify-content: flex-start !important;

    .btn {
      margin-left: 0 !important;
      margin-right: auto !important;
      align-self: flex-start !important;
    }
  }

  // Ensure proper RTL layout for Arabic
  .text-lg-end {
    text-align: left !important;
  }

  // Force button to extreme left in Arabic
  .col-lg-auto {
    &.button-container-arabic {
      margin-left: 0 !important;
      padding-left: 0 !important;

      .btn {
        float: left !important;
        clear: left !important;
      }
    }
  }
}

// Default larger profile image for Arabic
.profile-image-arabic {
  width: 85px;
  height: 85px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.button-container-arabic {
  text-align: left;
  display: flex;
  justify-content: flex-start;

  .btn {
    margin-left: 0;
    margin-right: auto;
    align-self: flex-start;
  }
}
