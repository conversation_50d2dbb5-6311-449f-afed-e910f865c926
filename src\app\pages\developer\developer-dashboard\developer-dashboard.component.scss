.dashboard-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.dashboard-content {
  padding: 20px;
  overflow-y: auto;
}

.dashboard-cards-container {
  margin-top: 20px;
}

.project-title,
.project-title-left {
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    opacity: 0.8;
  }
}

.project-title-left {
  .text-dark {
    font-size: 1rem;
  }

  i {
    font-size: 1.1rem;
  }
}

.main-dashboard-container {
  display: flex;
  flex-direction: row;
  gap: 1px;
}

.analysis-cards-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1px;
  flex: 55%;
}

.card {
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
  border: none;

  &.bg-primary,
  &.bg-success,
  &.bg-danger,
  &.bg-warning {
    .card-header {
      border-bottom: none;
    }
  }
}

.badge {
  font-size: 0.85rem;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
}

.fs-7 {
  font-size: 0.85rem !important;
}

.fs-2 {
  font-size: 1.75rem !important;
  font-weight: 600;
}

.opacity-75 {
  opacity: 0.75 !important;
}

.bullet {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

@media (max-width: 1200px) {
  .col-xl-4,
  .col-xl-8 {
    width: 100%;
    margin-bottom: 20px;
  }

  .col-xl-6 {
    width: 100%;
    margin-bottom: 20px;
  }

  .main-dashboard-container {
    flex-direction: column;
  }

  .analysis-cards-container {
    flex: 1 1 100%;
  }

  .dashboard-pie-chart {
    flex: 1 1 100%;
    justify-content: center;
    margin-top: 20px;
  }
}

@media (max-width: 768px) {
  .dashboard-content {
    padding: 10px;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start !important;

    .card-toolbar {
      margin-top: 10px;
      width: 100%;
    }
  }

  .analysis-cards-container {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(4, 1fr);
  }
}

// Top 5 Models Simple Styles
.top-models-section {
  .card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }
  }

  .empty-state {
    .symbol {
      animation: pulse 2s infinite;
    }
  }
}

// Simple animation
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

// Date filters styles with RTL support
.top-models-section {
  .card-toolbar {
    .form-control-sm {
      border: 1px solid #e1e3ea;
      border-radius: 6px;
      font-size: 0.85rem;

      &:focus {
        border-color: #0d47a1;
        box-shadow: 0 0 0 0.2rem rgba(13, 71, 161, 0.25);
      }
    }

    .btn-sm {
      padding: 0.375rem 0.75rem;
      font-size: 0.85rem;
      border-radius: 6px;
    }
  }
}

// RTL Support for Top Models Section
:host-context(html[lang="ar"]) {
  .top-models-section {
    direction: rtl;
    text-align: right;

    .card {
      direction: rtl;

      .card-header {
        text-align: right;

        h3 {
          font-family: 'Hacen Liner Screen St', sans-serif;
          text-align: right;

          .card-label {
            font-size: 1.3rem !important;
            font-weight: 700;
          }

          .text-mid-blue {
            font-size: 0.9rem !important;
          }
        }

        .card-toolbar {
          .d-flex {
            flex-direction: row;
            gap: 1rem;
          }

          .form-label {
            font-family: 'Hacen Liner Screen St', sans-serif;
            margin-left: 0.5rem !important;
            margin-right: 0 !important;
          }

          .form-control-sm {
            direction: rtl;
            text-align: right;
            font-family: 'Hacen Liner Screen St', sans-serif;
          }

          .btn {
            font-family: 'Hacen Liner Screen St', sans-serif;

            i {
              margin-left: 0.5rem !important;
              margin-right: 0 !important;
            }
          }
        }
      }

      .card-body {
        direction: rtl;
        text-align: right;

        .card {
          direction: rtl;

          .position-absolute {
            right: 0 !important;
            left: auto !important;

            .badge {
              border-radius: 0 0 0 8px !important;
              font-family: 'Hacen Liner Screen St', sans-serif;
            }
          }

          .card-body {
            text-align: right;

            h5 {
              font-family: 'Hacen Liner Screen St', sans-serif;
              text-align: right;
            }

            .d-flex.justify-content-between {
              flex-direction: row-reverse;

              span {
                font-family: 'Hacen Liner Screen St', sans-serif;
              }
            }

            .text-center {
              text-align: center !important;

              a {
                font-family: 'Hacen Liner Screen St', sans-serif;

                i {
                  margin-left: 0.25rem !important;
                  margin-right: 0 !important;
                }
              }
            }
          }
        }

        .empty-state {
          text-align: center;

          h4, p {
            font-family: 'Hacen Liner Screen St', sans-serif;
          }
        }
      }
    }
  }
}

// Additional RTL classes
.rtl-section {
  direction: rtl;
}

.rtl-header {
  direction: rtl;
  text-align: right;
}

.rtl-toolbar {
  direction: rtl;

  .d-flex {
    flex-direction: row-reverse;
  }
}

.rtl-filters {
  direction: rtl;
  flex-direction: row-reverse;
}

.rtl-body {
  direction: rtl;
  text-align: right;
}

.rtl-card {
  direction: rtl;
  text-align: right;
}

.rtl-card-body {
  direction: rtl;
  text-align: right;
}

.rtl-info-row {
  direction: rtl;
  flex-direction: row-reverse;
}

.rtl-empty {
  direction: rtl;
  text-align: center;
}

// Responsive adjustments
@media (max-width: 768px) {
  .top-models-section {
    .card-body {
      padding: 1rem !important;
    }

    .badge {
      font-size: 0.75rem !important;
      padding: 0.4rem 0.8rem !important;
    }

    .card-toolbar {
      .d-flex {
        flex-direction: column;
        gap: 0.75rem !important;

        > div {
          width: 100%;
          justify-content: space-between;
        }

        .form-control-sm {
          width: auto !important;
          flex: 1;
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .top-models-section {
    .card-header {
      flex-direction: column;
      align-items: flex-start !important;

      .card-toolbar {
        margin-top: 1rem;
        width: 100%;
      }
    }
  }
}

// RTL Support for Developer Dashboard
.rtl-layout {
  direction: rtl;
  text-align: right;

  .card-title {
    font-family: 'Hacen Liner Screen St', sans-serif;
    font-weight: bold;
  }

  .card-label {
    font-family: 'Hacen Liner Screen St', sans-serif;
    font-size: 1.3rem;
  }

  .btn {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .form-control {
    text-align: right;
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .form-label {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .table {
    th, td {
      text-align: right;
      font-family: 'Hacen Liner Screen St', sans-serif;
    }

    th {
      font-size: 1.1rem;
      font-weight: bold;
    }
  }

  .badge {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .text-gray-600, .text-gray-500 {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  h4, h5 {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  // Reverse margins and paddings
  .me-1, .me-2 {
    margin-left: 0.25rem !important;
    margin-right: 0 !important;
  }

  .ms-1 {
    margin-right: 0.25rem !important;
    margin-left: 0 !important;
  }

  .ps-4 {
    padding-right: 1.5rem !important;
    padding-left: 0 !important;
  }

  // Model cards
  .d-flex.justify-content-between {
    flex-direction: row-reverse;
  }

  // Empty state
  .text-center {
    text-align: center !important;
  }

  // Card toolbar
  .card-toolbar {
    .d-flex {
      flex-direction: row-reverse;
    }
  }
}

canvas {
  max-height: 300px;
}

