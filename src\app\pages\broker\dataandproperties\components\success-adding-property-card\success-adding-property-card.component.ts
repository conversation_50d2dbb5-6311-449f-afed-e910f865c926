import { Component, EventEmitter, Output } from '@angular/core';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-success-adding-property-card',
  templateUrl: './success-adding-property-card.component.html',
  styleUrl: './success-adding-property-card.component.scss',
})
export class SuccessAddingPropertyCardComponent {
  @Output() backToTable = new EventEmitter<void>();

  constructor(public translationService: TranslationService) {}

  onBackToTable() {
    this.backToTable.emit();
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'PROPERTY_ADDED_SUCCESS': 'تم إضافة العقار بنجاح!',
        'BACK_TO_PROPERTIES_TABLE': 'العودة إلى جدول العقارات'
      },
      'en': {
        'PROPERTY_ADDED_SUCCESS': 'Property added successfully!',
        'BACK_TO_PROPERTIES_TABLE': 'Back to properties table'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }
}
