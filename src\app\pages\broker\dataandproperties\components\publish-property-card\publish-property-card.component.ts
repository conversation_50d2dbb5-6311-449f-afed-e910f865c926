import { Component, EventEmitter, Output } from '@angular/core';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-publish-property-card',
  templateUrl: './publish-property-card.component.html',
  styleUrl: './publish-property-card.component.scss',
})
export class PublishPropertyCardComponent {
  @Output() backToTable = new EventEmitter<void>();

  constructor(public translationService: TranslationService) {}

  onBackToTable() {
    this.backToTable.emit();
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'PROPERTY_PUBLISHED_SUCCESS': 'تم نشر العقار بنجاح!',
        'BACK_TO_PROPERTIES_TABLE': 'العودة إلى جدول العقارات'
      },
      'en': {
        'PROPERTY_PUBLISHED_SUCCESS': 'Property published successfully!',
        'BACK_TO_PROPERTIES_TABLE': 'Back to properties table'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }
}
