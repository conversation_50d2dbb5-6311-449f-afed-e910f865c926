<div
  class="card-body d-flex flex-column justify-content-between mt-9 bgi-no-repeat bgi-size-cover bgi-position-x-center pb-0"
  [class.rtl-layout]="translationService.getCurrentLanguage() === 'ar'">
  <img class="mx-auto h-150px h-lg-200px theme-light-show" src="../../../../assets/media/broker/Leader-pana 1.png"
    alt="" />
  <img class="mx-auto h-150px h-lg-200px theme-dark-show" src="../../../../assets/media/broker/Leader-pana 1.png"
    alt="" />
  <div class="mb-10 mt-10">
    <div class="fs-2hx fw-bold text-dark-blue text-center mb-1">
      <span class="me-2">{{ getTranslatedText('PROPERTY_ADDED_SUCCESS') }}</span>
    </div>

    <div class="text-center mb-2">
      <button (click)="onBackToTable()" class="btn btn-md btn-dark-blue fw-bold">
        <app-keenicon name="double-left" class="text-white"></app-keenicon>
        {{ getTranslatedText('BACK_TO_PROPERTIES_TABLE') }}
      </button>
    </div>
  </div>
</div>
